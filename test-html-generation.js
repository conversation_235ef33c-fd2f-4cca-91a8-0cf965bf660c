import { ReportGenerator } from './server/pdf-generator.js';

async function generateAllCompanyReports() {
  const companies = ['vz', 'cvs', 'acn', 'ma'];
  
  for (const symbol of companies) {
    try {
      console.log(`\nGenerating ERA report for ${symbol.toUpperCase()}...`);
      
      const htmlFilePath = await ReportGenerator.generateHTMLFile({
        reportType: 'era',
        symbol
      });
      
      console.log(`✅ HTML file generated: ${htmlFilePath}`);
      
    } catch (error) {
      console.error(`❌ Error generating HTML for ${symbol}:`, error.message);
    }
  }
  
  console.log('\n🎉 All HTML reports generated!');
  console.log('\nTo convert any HTML to PDF:');
  console.log('1. Open the HTML file in Chrome/Safari');
  console.log('2. Press Ctrl+P (or Cmd+P on Mac)');
  console.log('3. Choose "Save as PDF" as destination'); 
  console.log('4. Enable "Background graphics" in print options');
}

generateAllCompanyReports();