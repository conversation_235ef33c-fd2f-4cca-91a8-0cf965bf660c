import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import puppeteer from "puppeteer";
import chromium from "@sparticuz/chromium";
import { ChartGenerator } from "./chart-generator.js";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

interface HTMLGenerationOptions {
  reportType: 'sei' | 'era';
  symbol: string;
}

interface PDFGenerationOptions {
  reportType: 'sei' | 'era';
  symbol: string;
  format?: 'A4' | 'Letter';
  landscape?: boolean;
}

interface SectionData {
  title: string;
  component: string;
  data: any;
}

export class ReportGenerator {
  private static async getReportSections(reportType: 'sei' | 'era', symbol: string): Promise<SectionData[]> {
    const companiesPath = path.join(__dirname, '../client/public/data/companies', symbol.toLowerCase());
    
    if (reportType === 'sei') {
      const sections = [
        'executive-summary',
        'enterprise-layer',
        'products-services',
        'customer-engagement',
        'organizational-maturity',
        'execution-roadmap'
      ];
      
      const sectionData: SectionData[] = [];
      
      for (const section of sections) {
        const dataPath = path.join(companiesPath, `${section}.json`);
        try {
          if (fs.existsSync(dataPath)) {
            const data = JSON.parse(fs.readFileSync(dataPath, 'utf-8'));
            sectionData.push({
              title: this.getSectionTitle(section),
              component: section,
              data
            });
          }
        } catch (error) {
          console.warn(`Warning: Could not load ${section} data:`, error);
        }
      }
      
      return sectionData;
    } else {
      // ERA Report sections
      const sections = [
        'executive-summary-dashboard',
        'company-context-benchmark-explorer',
        'execution-maturity-joy-score-deep-dive',
        'agility-simulator',
        'value-leakage-financial-impact',
        'strategic-lens-engine',
        'best-practice-library',
        'recommendations-roadmap'
      ];
      
      const sectionData: SectionData[] = [];
      const eraPath = path.join(companiesPath, 'era-report');
      
      for (const section of sections) {
        const dataPath = path.join(eraPath, `${section}.json`);
        try {
          if (fs.existsSync(dataPath)) {
            const data = JSON.parse(fs.readFileSync(dataPath, 'utf-8'));
            sectionData.push({
              title: this.getSectionTitle(section),
              component: section,
              data
            });
          }
        } catch (error) {
          console.warn(`Warning: Could not load ${section} data:`, error);
        }
      }
      
      return sectionData;
    }
  }
  
  private static getSectionTitle(section: string): string {
    const titles: Record<string, string> = {
      // SEI Report titles
      'executive-summary': 'Executive Summary',
      'enterprise-layer': 'Enterprise Layer',
      'products-services': 'Products & Services',
      'customer-engagement': 'Customer Engagement',
      'organizational-maturity': 'Organizational Maturity',
      'execution-roadmap': 'Execution Roadmap',
      
      // ERA Report titles
      'executive-summary-dashboard': 'Executive Summary',
      'company-context-benchmark-explorer': 'Financial Performance & Market Position',
      'execution-maturity-joy-score-deep-dive': 'Organizational Maturity & Execution Agility',
      'agility-simulator': 'Strategic Agility Assessment',
      'value-leakage-financial-impact': 'Risk Analysis & Value Impact',
      'strategic-lens-engine': 'Strategic Alternatives & Scenario Analysis',
      'best-practice-library': 'Best Practices & Benchmarks',
      'recommendations-roadmap': 'Strategic Recommendations & Implementation'
    };
    
    return titles[section] || section.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  public static async generateHTMLFile(options: HTMLGenerationOptions): Promise<string> {
    const { reportType, symbol } = options;
    
    console.log(`HTML Generation started for ${reportType.toUpperCase()} report, company: ${symbol}`);
    
    const sections = await this.getReportSections(reportType, symbol);
    console.log(`Found ${sections.length} sections for ${reportType.toUpperCase()} report:`);
    sections.forEach((section, index) => {
      console.log(`  ${index + 1}. ${section.title} (${section.component})`);
    });
    
    if (sections.length === 0) {
      throw new Error(`No data found for ${reportType.toUpperCase()} report for company ${symbol}`);
    }
    
    console.log('Generating HTML content...');
    const html = await this.generateHTML(sections, reportType, symbol);
    console.log(`HTML generated, length: ${html.length} characters`);
    
    // Write HTML file to company data directory
    const htmlFilePath = path.join(__dirname, `../client/public/data/companies/${symbol.toLowerCase()}/${reportType}-report.html`);
    
    // Ensure directory exists (it should already exist with JSON data)
    const dir = path.dirname(htmlFilePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(htmlFilePath, html, 'utf-8');
    console.log(`HTML file saved to: ${htmlFilePath}`);
    
    return htmlFilePath;
  }

  public static async generatePDF(options: PDFGenerationOptions): Promise<Buffer> {
    const { reportType, symbol, format = 'A4', landscape = false } = options;
    
    console.log(`PDF Generation started for ${reportType.toUpperCase()} report, company: ${symbol}`);
    
    const sections = await this.getReportSections(reportType, symbol);
    console.log(`Found ${sections.length} sections for ${reportType.toUpperCase()} report`);
    
    if (sections.length === 0) {
      throw new Error(`No data found for ${reportType.toUpperCase()} report for company ${symbol}`);
    }
    
    // Generate HTML with charts
    const html = await this.generateHTML(sections, reportType, symbol);
    
    // Use @sparticuz/chromium for Docker environments
    const browser = await puppeteer.launch({
      args: chromium.args,
      defaultViewport: chromium.defaultViewport,
      executablePath: await chromium.executablePath(),
      headless: chromium.headless
    });
    
    try {
      const page = await browser.newPage();
      await page.setContent(html, { 
        waitUntil: 'domcontentloaded',
        timeout: 30000
      });
      
      // Wait for charts to load
      await page.waitForTimeout(2000);
      
      const pdfBuffer = Buffer.from(await page.pdf({
        format: format as any,
        landscape: landscape,
        printBackground: true,
        margin: { 
          top: '0.5in', 
          right: '0.5in', 
          bottom: '0.5in', 
          left: '0.5in' 
        },
        preferCSSPageSize: true
      }));
      
      console.log(`PDF generated successfully, size: ${pdfBuffer.length} bytes`);
      return pdfBuffer;
      
    } finally {
      await browser.close();
    }
  }


  private static async generateHTML(sections: SectionData[], reportType: 'sei' | 'era', companySymbol: string): Promise<string> {
    const reportTitle = reportType.toUpperCase() === 'SEI' ? 'Strategic Enterprise Intelligence' : 'Enterprise Readiness Assessment';
    const companyName = this.getCompanyName(companySymbol);
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${reportTitle} Report - ${companyName}</title>
    <style>
        ${this.getERAStyles()}
    </style>
</head>
<body>
    <!-- ERA Cover Page -->
    <div class="cover-page">
        <div class="cover-content">
            <h1>Enterprise Readiness Assessment</h1>
            <div class="subtitle">ERA Report</div>
            <h2>${companyName}</h2>
            <div class="subtitle">Execution & Value Creation Analysis</div>
            <div class="era-tagline">Quantifying Execution Maturity, Agility, and Enterprise Value Impact</div>
            <div class="meta">
                <p>Generated on ${new Date().toLocaleDateString('en-US', { 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}</p>
                <p>Confidential Analysis | Symbol: ${companySymbol.toUpperCase()}</p>
            </div>
        </div>
    </div>
    
    <div class="page-break"></div>
    
    <!-- Report Sections -->
    <div class="report-container">
        ${await Promise.all(sections.map(async (section, index) => await this.renderSection(section, index + 1))).then(results => results.join(''))}
    </div>
    
    <!-- Footer -->
    <div class="page-footer">
        <p>🤖 Generated with Claude Code | Rejoyce Analytics Platform | ${new Date().getFullYear()}</p>
    </div>
</body>
</html>`;
  }

  private static getERAStyles(): string {
    return `
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1a1a1a;
            background: white;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                font-size: 12pt;
                line-height: 1.4;
            }
            .page-break {
                page-break-before: always;
            }
            .avoid-break {
                page-break-inside: avoid;
            }
            .cover-page {
                page-break-after: always;
            }
            .section {
                page-break-inside: avoid;
                margin-bottom: 30px;
            }
        }
        
        @page {
            size: A4;
            margin: 1in;
        }
        
        .report-container {
            max-width: none;
            margin: 0;
            padding: 20px;
        }
        
        /* ERA Cover page */
        .cover-page {
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #1e40af 100%);
            color: white;
            margin: -20px;
            padding: 40px;
        }
        
        .cover-page h1 {
            font-size: 52px;
            font-weight: 700;
            margin-bottom: 15px;
        }
        
        .cover-page .subtitle {
            font-size: 22px;
            font-weight: 300;
            margin-bottom: 8px;
        }
        
        .cover-page h2 {
            font-size: 36px;
            font-weight: 600;
            margin-bottom: 40px;
        }
        
        .era-tagline {
            font-size: 18px;
            font-weight: 500;
            margin-top: 20px;
            font-style: italic;
        }
        
        /* Section styles */
        .section {
            margin: 40px 0;
            padding: 30px 0;
        }
        
        .section-header {
            margin-bottom: 30px;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 15px;
        }
        
        .section-title {
            font-size: 32px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 10px;
        }
        
        .section-number {
            font-size: 14px;
            font-weight: 600;
            color: #667eea;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        /* ERA Executive Summary */
        .era-executive-summary {
            background: white;
            padding: 40px;
            margin: 20px 0;
        }
        
        .executive-headline h2 {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 12px;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin: 30px 0;
            justify-items: center;
        }
        
        .kpi-gauge {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .findings-section {
            margin: 40px 0;
            background: #f8fafc;
            padding: 30px;
            border-radius: 12px;
            border-left: 4px solid #3b82f6;
        }
        
        .findings-list {
            list-style: none;
            padding: 0;
        }
        
        .findings-list li {
            margin: 12px 0;
            padding: 12px 0;
            border-bottom: 1px solid #e2e8f0;
            font-size: 14px;
        }
        
        .priorities-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 16px;
            margin: 25px 0;
        }
        
        .priority-summary-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin: 16px 0;
        }
        
        .recommendation-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
            margin: 20px 0;
        }
        
        .page-footer {
            margin-top: 40px;
            padding: 20px 0;
            border-top: 1px solid #e2e8f0;
            font-size: 12px;
            color: #718096;
            text-align: center;
        }
        
        /* Additional section styles */
        .agility-section, .best-practices, .strategic-analysis, .risk-analysis {
            background: white;
            padding: 30px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #1e293b;
            margin: 10px 0;
        }
        
        .practices-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin: 25px 0;
        }
        
        .practice-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #3b82f6;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
            justify-items: center;
        }
        
        .section-error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 20px;
            color: #991b1b;
        }
        
        /* KPI and metrics styling */
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        
        .kpi-card {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .kpi-card.critical { border-left: 4px solid #dc2626; }
        .kpi-card.warning { border-left: 4px solid #f59e0b; }
        .kpi-card.normal { border-left: 4px solid #10b981; }
        
        .kpi-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .kpi-target, .kpi-benchmark {
            font-size: 12px;
            color: #64748b;
        }
        
        /* Table styling */
        .metrics-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .metrics-table th, .metrics-table td {
            border: 1px solid #e2e8f0;
            padding: 12px;
            text-align: left;
        }
        
        .metrics-table th {
            background: #f8fafc;
            font-weight: bold;
        }
        
        .status-above { color: #059669; font-weight: bold; }
        .status-below { color: #dc2626; font-weight: bold; }
        .status-average { color: #64748b; }
        
        /* Dimensions and scoring */
        .dimensions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .dimension-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .score-value {
            font-size: 20px;
            font-weight: bold;
            color: #1e293b;
            margin: 8px 0;
        }
        
        .score-bar {
            width: 100%;
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            margin: 8px 0;
        }
        
        .score-fill {
            height: 100%;
            background: #3b82f6;
            border-radius: 3px;
        }
        
        /* Risk styling */
        .risk-list {
            margin: 25px 0;
        }
        
        .risk-item {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .risk-item.critical { border-left: 4px solid #dc2626; }
        .risk-item.warning { border-left: 4px solid #f59e0b; }
        
        .risk-details {
            display: flex;
            gap: 20px;
            margin: 10px 0;
            font-size: 14px;
            color: #64748b;
        }
        
        /* Strategic options */
        .strategic-options {
            margin: 25px 0;
        }
        
        .strategy-option {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        /* Value leakage */
        .value-leakage-summary {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
        }
        
        .total-leakage {
            font-size: 18px;
            color: #dc2626;
            margin-bottom: 20px;
        }
        
        .leakage-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .leakage-item:last-child {
            border-bottom: none;
        }
        
        .category {
            font-weight: bold;
            flex: 2;
        }
        
        .amount {
            font-weight: bold;
            color: #dc2626;
            flex: 1;
        }
        
        .percentage {
            color: #64748b;
            flex: 1;
        }
    `;
  }

  private static async renderSection(section: SectionData, sectionNumber: number): Promise<string> {
    console.log(`Rendering section ${sectionNumber}: ${section.title} (${section.component})`);
    
    const sectionContent = await this.renderSectionContent(section);
    
    return `
    <div class="section page-break">
        <div class="section-header avoid-break">
            <div class="section-number">Section ${sectionNumber}</div>
            <h2 class="section-title">${section.title}</h2>
        </div>
        
        <div class="section-content">
            ${sectionContent}
        </div>
    </div>`;
  }

  private static async renderSectionContent(section: SectionData): Promise<string> {
    const data = section.data;
    
    try {
      switch (section.component) {
        case 'executive-summary-dashboard':
          return await this.renderExecutiveSummaryDashboard(data);
        case 'company-context-benchmark-explorer':
          return await this.renderCompanyContext(data);
        case 'execution-maturity-joy-score-deep-dive':
          return await this.renderJoyScoreDeepDive(data);
        case 'agility-simulator':
          return await this.renderAgilitySimulator(data);
        case 'value-leakage-financial-impact':
          return await this.renderValueLeakage(data);
        case 'strategic-lens-engine':
          return await this.renderStrategicLens(data);
        case 'best-practice-library':
          return await this.renderBestPractices(data);
        case 'recommendations-roadmap':
          return await this.renderRecommendations(data);
        default:
          console.log(`Rendering generic section for: ${section.component}`);
          return this.renderGenericSection(data);
      }
    } catch (error) {
      console.error(`Error rendering section ${section.component}:`, error);
      return `<div class="section-error">
        <h3>Error loading ${section.title}</h3>
        <p>Unable to render section content. Please check the data format.</p>
      </div>`;
    }
  }

  private static async renderExecutiveSummaryDashboard(data: any): Promise<string> {
    const companySymbol = data.companySymbol || 'Company';
    const joyScore = data.readinessSnapshot?.joyScore || { value: 3.0 };
    const valueLeakage = data.valueLeakageOverview?.totalLeakage || { value: '$5.5B' };
    const keyFindings = data.keyFindings || [];
    const executiveKPIs = data.executiveKPIs || [];
    
    try {
      // Generate simple charts
      const [joyScoreGauge, valueLeakageChart] = await Promise.all([
        ChartGenerator.generateChartDataURL({
          type: 'gauge',
          data: { 
            value: joyScore.value, 
            max: 5, 
            label: 'Joy Score', 
            color: '#667eea' 
          },
          title: 'Joy Score',
          width: 300, 
          height: 250
        }),
        ChartGenerator.generateChartDataURL({
          type: 'bar',
          data: {
            labels: (data.valueLeakageOverview?.breakdown || []).map((item: any) => item.category.split(' ')[0]),
            datasets: [{
              label: 'Value Leakage ($B)',
              data: (data.valueLeakageOverview?.breakdown || []).map((item: any) => parseFloat(item.amount.replace('$', '').replace('B', ''))),
              backgroundColor: ['#dc2626', '#f59e0b', '#8b5cf6']
            }]
          },
          title: 'Value Leakage Breakdown',
          width: 500,
          height: 300
        })
      ]);

      return `
        <div class="era-executive-summary">
          <div class="executive-headline">
            <h2>${companySymbol.toUpperCase()}'s Execution Gaps: ${valueLeakage.value} Value at Risk</h2>
            <p class="subtitle">Joy Score: ${joyScore.value}/5.0 (${joyScore.level || 'Automated'})</p>
          </div>
          
          <div class="charts-section">
            <div class="chart-container">
              <img src="${joyScoreGauge}" alt="Joy Score" style="width: 300px; height: 250px;" />
            </div>
            <div class="chart-container">
              <img src="${valueLeakageChart}" alt="Value Leakage" style="width: 500px; height: 300px;" />
            </div>
          </div>
          
          <div class="kpi-summary">
            <h3>Key Performance Indicators</h3>
            <div class="kpi-grid">
              ${executiveKPIs.map((kpi: any) => `
                <div class="kpi-card ${kpi.status}">
                  <h4>${kpi.metric}</h4>
                  <div class="kpi-value">${kpi.current}</div>
                  <div class="kpi-target">Target: ${kpi.target}</div>
                  <div class="kpi-benchmark">Benchmark: ${kpi.benchmark}</div>
                </div>
              `).join('')}
            </div>
          </div>
          
          <div class="findings-section">
            <h3>Key Findings</h3>
            <ul class="findings-list">
              ${keyFindings.map((finding: any) => `
                <li class="${finding.status}">
                  <strong>${finding.title}:</strong> ${finding.description}
                  ${finding.impact ? `<span class="impact">(Impact: ${finding.impact})</span>` : ''}
                </li>
              `).join('')}
            </ul>
          </div>
        </div>
      `;
    } catch (chartError) {
      console.warn('Chart generation failed, using fallback:', chartError);
      // Fallback without charts
      return `
        <div class="era-executive-summary">
          <div class="executive-headline">
            <h2>${companySymbol.toUpperCase()}'s Execution Gaps: ${valueLeakage.value} Value at Risk</h2>
            <p class="subtitle">Joy Score: ${joyScore.value}/5.0 (${joyScore.level || 'Automated'})</p>
          </div>
          
          <div class="kpi-summary">
            <h3>Key Performance Indicators</h3>
            <div class="kpi-grid">
              ${executiveKPIs.map((kpi: any) => `
                <div class="kpi-card ${kpi.status}">
                  <h4>${kpi.metric}</h4>
                  <div class="kpi-value">${kpi.current}</div>
                  <div class="kpi-target">Target: ${kpi.target}</div>
                  <div class="kpi-benchmark">Benchmark: ${kpi.benchmark}</div>
                </div>
              `).join('')}
            </div>
          </div>
          
          <div class="findings-section">
            <h3>Key Findings</h3>
            <ul class="findings-list">
              ${keyFindings.map((finding: any) => `
                <li class="${finding.status}">
                  <strong>${finding.title}:</strong> ${finding.description}
                  ${finding.impact ? `<span class="impact">(Impact: ${finding.impact})</span>` : ''}
                </li>
              `).join('')}
            </ul>
          </div>
        </div>
      `;
    }
  }

  private static async renderRecommendations(data: any): Promise<string> {
    const priorities = data.strategicPriorities || [];
    
    return `
      <div class="recommendations">
        <h3>Strategic Recommendations</h3>
        
        ${priorities.map((priority: any, index: number) => `
          <div class="priority-${index + 1} recommendation-card">
            <h4>Priority ${index + 1}: ${priority.priority}</h4>
            <p><strong>Description:</strong> ${priority.description}</p>
            <p><strong>Timeline:</strong> ${priority.timeline}</p>
            <p><strong>Impact:</strong> ${priority.impact}</p>
            <div class="progress-bar">
              <div class="progress-fill" style="width: ${priority.progress || 0}%"></div>
            </div>
          </div>
        `).join('')}
        
        <div class="hundred-day-plan">
          <h3>100-Day Implementation Plan</h3>
          <div class="timeline">
            <div class="phase">
              <h4>Days 0-30: Foundation & Quick Wins</h4>
              <ul>
                <li>Customer experience assessment and quick fixes</li>
                <li>5G service development acceleration</li>
                <li>Innovation process optimization</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private static async renderCompanyContext(data: any): Promise<string> {
    const benchmarkComparison = data.benchmarkComparison || {};
    const metrics = benchmarkComparison.metrics || [];
    
    return `
      <div class="financial-performance">
        <h3>Financial Performance & Market Position</h3>
        <p><strong>Mixed Performance Profile:</strong> Strong revenue growth offset by margin pressures and leverage concerns.</p>
        
        <div class="benchmark-table">
          <h4>Peer Benchmark Comparison</h4>
          <table class="metrics-table">
            <thead>
              <tr>
                <th>Metric</th>
                <th>Company</th>
                <th>Industry Avg</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              ${metrics.map((metric: any) => `
                <tr>
                  <td>${metric.metric}</td>
                  <td>${metric.verizon || metric.company || 'N/A'}</td>
                  <td>${metric.industry_avg || 'N/A'}</td>
                  <td class="status-${this.getMetricStatus(metric)}">
                    ${this.getMetricStatus(metric)}
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      </div>
    `;
  }

  private static async renderJoyScoreDeepDive(data: any): Promise<string> {
    const joyScore = data.readinessSnapshot?.joyScore || {};
    const breakdown = joyScore.breakdown || {};
    
    try {
      // Generate radar chart for Joy Score dimensions
      const radarChart = await ChartGenerator.generateChartDataURL({
        type: 'radar',
        data: {
          labels: Object.keys(breakdown).map(key => key.charAt(0).toUpperCase() + key.slice(1)),
          companyScores: Object.values(breakdown),
          benchmarkScores: Object.keys(breakdown).map(() => joyScore.industryAverage || 3.1)
        },
        title: 'Joy Score: Organizational Maturity Profile',
        width: 500, 
        height: 400
      });

      return `
        <div class="joy-score-analysis">
          <h3>Organizational Maturity Assessment</h3>
          <p><strong>${joyScore.level || 'Mid-Level'} Maturity:</strong> ${joyScore.description || 'Organization shows moderate execution readiness with particular gaps in culture and process agility.'}</p>
          
          <div class="radar-section">
            <img src="${radarChart}" alt="Joy Score Radar" style="width: 500px; height: 400px; display: block; margin: 20px auto;" />
          </div>
          
          <div class="joy-score-breakdown">
            <h4>Joy Score Dimensions (Scale: 1-5)</h4>
            <div class="dimensions-grid">
              ${Object.entries(breakdown).map(([dimension, score]) => `
                <div class="dimension-card">
                  <h5>${dimension.charAt(0).toUpperCase() + dimension.slice(1)}</h5>
                  <div class="score-value">${score}/5.0</div>
                  <div class="score-bar">
                    <div class="score-fill" style="width: ${(Number(score) / 5) * 100}%"></div>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          
          <div class="maturity-insights">
            <div class="culture-insight">
              <h4>Key Insights</h4>
              <p>Industry Average: ${joyScore.industryAverage || 'N/A'} | Industry Leaders: ${joyScore.industryLeaders || 'N/A'}</p>
            </div>
          </div>
        </div>
      `;
    } catch (chartError) {
      console.warn('Chart generation failed for Joy Score, using fallback:', chartError);
      return `
        <div class="joy-score-analysis">
          <h3>Organizational Maturity Assessment</h3>
          <p><strong>${joyScore.level || 'Mid-Level'} Maturity:</strong> ${joyScore.description || 'Organization shows moderate execution readiness with particular gaps in culture and process agility.'}</p>
          
          <div class="joy-score-breakdown">
            <h4>Joy Score Dimensions (Scale: 1-5)</h4>
            <div class="dimensions-grid">
              ${Object.entries(breakdown).map(([dimension, score]) => `
                <div class="dimension-card">
                  <h5>${dimension.charAt(0).toUpperCase() + dimension.slice(1)}</h5>
                  <div class="score-value">${score}/5.0</div>
                  <div class="score-bar">
                    <div class="score-fill" style="width: ${(Number(score) / 5) * 100}%"></div>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          
          <div class="maturity-insights">
            <div class="culture-insight">
              <h4>Key Insights</h4>
              <p>Industry Average: ${joyScore.industryAverage || 'N/A'} | Industry Leaders: ${joyScore.industryLeaders || 'N/A'}</p>
            </div>
          </div>
        </div>
      `;
    }
  }

  private static async renderValueLeakage(data: any): Promise<string> {
    const riskAssessment = data.riskAssessment || [];
    const valueLeakage = data.valueLeakageOverview || {};
    
    return `
      <div class="risk-analysis">
        <h3>Risk Analysis & Value Impact</h3>
        <p><strong>Critical Risk Areas:</strong> ${riskAssessment.length} key execution risks identified with aggregate value impact of ${valueLeakage.totalLeakage?.value || '$5.5B'}.</p>
        
        <div class="risk-list">
          ${riskAssessment.map((risk: any) => `
            <div class="risk-item ${risk.status}">
              <h4>${risk.risk}</h4>
              <div class="risk-details">
                <span class="probability">Probability: ${risk.probability}</span>
                <span class="impact">Impact: ${risk.impact}</span>
              </div>
              <p class="risk-description">${risk.description}</p>
              <p class="mitigation"><strong>Mitigation:</strong> ${risk.mitigation}</p>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  private static async renderStrategicLens(data: any): Promise<string> {
    return `
      <div class="strategic-analysis">
        <h3>Strategic Alternatives & Scenario Analysis</h3>
        <p><strong>Growth & Expansion Recommended:</strong> Offers highest value potential despite moderate execution complexity.</p>
        
        <div class="strategic-options">
          <div class="strategy-option">
            <h4>Growth & Expansion</h4>
            <p><strong>Value Potential:</strong> High | <strong>Feasibility:</strong> Medium</p>
            <p>Focus on 5G monetization and customer experience transformation</p>
          </div>
          
          <div class="strategy-option">
            <h4>Cost Optimization</h4>
            <p><strong>Value Potential:</strong> Medium | <strong>Feasibility:</strong> High</p>
            <p>Network efficiency and operational excellence initiatives</p>
          </div>
          
          <div class="strategy-option">
            <h4>Innovation Acceleration</h4>
            <p><strong>Value Potential:</strong> High | <strong>Feasibility:</strong> Low</p>
            <p>Cultural transformation and agile development capabilities</p>
          </div>
        </div>
      </div>
    `;
  }

  private static async renderAgilitySimulator(data: any): Promise<string> {
    return `
      <div class="agility-section">
        <h3>Strategic Agility Assessment</h3>
        <p><strong>Current Agility Level:</strong> ${data.agilityLevel?.level || 'Structured'} with ${data.agilityLevel?.responseTime || '14-month'} average response time.</p>
        
        <div class="agility-metrics">
          <div class="metric-card">
            <h4>Response Time</h4>
            <p class="metric-value">${data.agilityLevel?.responseTime || '14 months'}</p>
            <p class="benchmark">vs ${data.agilityLevel?.benchmarkComparison || 'industry average'}</p>
          </div>
        </div>
        
        <div class="improvement-areas">
          <h4>Key Improvement Areas</h4>
          <ul>
            <li>Decision-making speed</li>
            <li>Innovation pipeline management</li>
            <li>Cross-functional collaboration</li>
          </ul>
        </div>
      </div>
    `;
  }

  private static async renderBestPractices(data: any): Promise<string> {
    return `
      <div class="best-practices">
        <h3>Best Practices & Benchmarks</h3>
        <p><strong>Industry Leading Practices:</strong> Comparison with top performers and recommended practices for improvement.</p>
        
        <div class="practices-grid">
          <div class="practice-card">
            <h4>Customer Experience Excellence</h4>
            <p>Leading companies achieve >750 J.D. Power scores through omnichannel integration</p>
          </div>
          
          <div class="practice-card">
            <h4>5G Monetization</h4>
            <p>Top performers capture 15-20% revenue growth from 5G services</p>
          </div>
          
          <div class="practice-card">
            <h4>Innovation Culture</h4>
            <p>Agile leaders reduce time-to-market by 40-60% through cultural transformation</p>
          </div>
        </div>
      </div>
    `;
  }

  private static async renderRecommendations(data: any): Promise<string> {
    const priorities = data.strategicPriorities || [];
    
    return `
      <div class="recommendations">
        <h3>Strategic Recommendations</h3>
        
        ${priorities.map((priority: any, index: number) => `
          <div class="priority-${index + 1} recommendation-card">
            <h4>Priority ${index + 1}: ${priority.priority}</h4>
            <p><strong>Description:</strong> ${priority.description}</p>
            <p><strong>Timeline:</strong> ${priority.timeline}</p>
            <p><strong>Impact:</strong> ${priority.impact}</p>
            <div class="progress-bar">
              <div class="progress-fill" style="width: ${priority.progress || 0}%"></div>
            </div>
          </div>
        `).join('')}
        
        <div class="hundred-day-plan">
          <h3>100-Day Implementation Plan</h3>
          <div class="timeline">
            <div class="phase">
              <h4>Days 0-30: Foundation & Quick Wins</h4>
              <ul>
                <li>Customer experience assessment and quick fixes</li>
                <li>5G service development acceleration</li>
                <li>Innovation process optimization</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private static renderGenericSection(data: any): string {
    if (!data || typeof data !== 'object') {
      return '<p>No data available for this section</p>';
    }
    
    let content = '';
    Object.entries(data).forEach(([key, value]) => {
      content += `<div><h4>${key}</h4><p>${this.formatValue(value)}</p></div>`;
    });
    
    return content || '<p>No content available</p>';
  }

  private static formatValue(value: any): string {
    if (typeof value === 'string') return value;
    if (typeof value === 'number') return value.toString();
    if (Array.isArray(value)) return value.join(', ');
    if (typeof value === 'object') return 'Complex data - see analysis';
    return String(value);
  }

  private static getMetricStatus(metric: any): string {
    // Simple logic to determine status based on comparison
    const company = metric.verizon || metric.company;
    const avg = metric.industry_avg;
    
    if (!company || !avg) return 'unknown';
    
    const companyVal = parseFloat(String(company).replace(/[^0-9.-]/g, ''));
    const avgVal = parseFloat(String(avg).replace(/[^0-9.-]/g, ''));
    
    if (isNaN(companyVal) || isNaN(avgVal)) return 'unknown';
    
    if (companyVal > avgVal * 1.1) return 'above';
    if (companyVal < avgVal * 0.9) return 'below';
    return 'average';
  }

  private static getCompanyName(symbol: string): string {
    const companyNames: Record<string, string> = {
      'vz': 'Verizon Communications',
      'cvs': 'CVS Health Corporation',
      'acn': 'Accenture plc',
      'ma': 'Mastercard Incorporated'
    };
    
    return companyNames[symbol.toLowerCase()] || symbol.toUpperCase();
  }

}