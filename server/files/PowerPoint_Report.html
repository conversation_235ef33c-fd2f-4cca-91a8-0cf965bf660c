<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CVS Health ERA Board Presentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f0f0f0;
            overflow-x: hidden;
        }
        
        .presentation-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .slide {
            background: white;
            min-height: 700px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 40px;
            page-break-after: always;
            position: relative;
        }
        
        .slide-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #CC0000;
        }
        
        .slide-number {
            position: absolute;
            bottom: 20px;
            right: 30px;
            color: #666;
            font-size: 14px;
        }
        
        h1 {
            color: #CC0000;
            font-size: 32px;
            margin-bottom: 10px;
        }
        
        h2 {
            color: #333;
            font-size: 24px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        h3 {
            color: #CC0000;
            font-size: 20px;
            margin: 20px 0 10px;
        }
        
        .cvs-logo {
            color: #CC0000;
            font-size: 28px;
            font-weight: bold;
        }
        
        .zero-fog-logo {
            position: absolute;
            bottom: 20px;
            left: 30px;
            color: #333;
            font-size: 12px;
        }
        
        /* Chart Styles */
        .chart-container {
            margin: 20px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
        }
        
        .gauge-chart {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
        }
        
        .gauge {
            width: 200px;
            height: 100px;
            position: relative;
            border: 3px solid #ddd;
            border-bottom: none;
            border-radius: 100px 100px 0 0;
        }
        
        .gauge-fill {
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, #ff0000, #ffcc00, #00cc00);
            border-radius: 100px 100px 0 0;
            clip-path: polygon(0 100%, 65% 100%, 65% 0, 0 0);
        }
        
        .gauge-needle {
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 4px;
            height: 90px;
            background: #333;
            transform-origin: bottom;
            transform: translateX(-50%) rotate(-27deg);
        }
        
        .gauge-value {
            text-align: center;
            margin-top: 10px;
            font-size: 24px;
            font-weight: bold;
        }
        
        /* Bar Chart */
        .bar-chart {
            display: flex;
            align-items: flex-end;
            justify-content: space-around;
            height: 300px;
            margin: 20px 0;
            padding: 20px;
            border-left: 2px solid #333;
            border-bottom: 2px solid #333;
        }
        
        .bar {
            width: 80px;
            background: linear-gradient(to top, #CC0000, #ff6666);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-end;
            padding: 10px;
            border-radius: 4px 4px 0 0;
            transition: all 0.3s;
        }
        
        .bar:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 10px rgba(0,0,0,0.2);
        }
        
        .bar-value {
            color: white;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .bar-label {
            margin-top: 10px;
            text-align: center;
            font-size: 12px;
        }
        
        /* Priority Icons */
        .priorities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .priority-card {
            background: linear-gradient(135deg, #f5f5f5, #e0e0e0);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            transition: transform 0.3s;
        }
        
        .priority-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .priority-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        
        .priority-title {
            font-weight: bold;
            color: #CC0000;
            margin-bottom: 5px;
        }
        
        /* Risk Matrix */
        .risk-matrix {
            position: relative;
            width: 500px;
            height: 500px;
            border: 2px solid #333;
            margin: 30px auto;
            background: linear-gradient(to top right, #e8f5e9, #ffebee);
        }
        
        .risk-bubble {
            position: absolute;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .risk-bubble:hover {
            transform: scale(1.1);
            z-index: 10;
        }
        
        .risk-high {
            background: #d32f2f;
        }
        
        .risk-medium {
            background: #f57c00;
        }
        
        .risk-low {
            background: #388e3c;
        }
        
        .axis-label {
            position: absolute;
            font-weight: bold;
            color: #333;
        }
        
        .x-axis-label {
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .y-axis-label {
            left: -80px;
            top: 50%;
            transform: translateY(-50%) rotate(-90deg);
        }
        
        /* Timeline */
        .timeline {
            position: relative;
            padding: 20px 0;
            margin: 30px 0;
        }
        
        .timeline-line {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 3px;
            background: #CC0000;
        }
        
        .timeline-items {
            display: flex;
            justify-content: space-between;
            position: relative;
        }
        
        .timeline-item {
            background: white;
            border: 3px solid #CC0000;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            flex: 1;
            margin: 0 10px;
            z-index: 1;
        }
        
        .timeline-date {
            font-weight: bold;
            color: #CC0000;
            margin-bottom: 5px;
        }
        
        /* Spider Chart */
        .spider-chart {
            width: 400px;
            height: 400px;
            margin: 30px auto;
            position: relative;
        }
        
        .spider-svg {
            width: 100%;
            height: 100%;
        }
        
        /* Key Metrics */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: #f5f5f5;
            padding: 15px;
            border-left: 4px solid #CC0000;
            border-radius: 4px;
        }
        
        .metric-value {
            font-size: 28px;
            font-weight: bold;
            color: #CC0000;
        }
        
        .metric-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .metric-change {
            font-size: 14px;
            margin-top: 5px;
        }
        
        .positive {
            color: #4caf50;
        }
        
        .negative {
            color: #f44336;
        }
        
        /* Table Styles */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .data-table th {
            background: #CC0000;
            color: white;
            padding: 12px;
            text-align: left;
        }
        
        .data-table td {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }
        
        .data-table tr:hover {
            background: #f5f5f5;
        }
        
        .quartile-1 { background: #d4edda; }
        .quartile-2 { background: #fff3cd; }
        .quartile-3 { background: #f8d7da; }
        .quartile-4 { background: #f5c6cb; }
        
        /* Bullet Points */
        .bullet-list {
            margin: 20px 0;
            padding-left: 30px;
        }
        
        .bullet-list li {
            margin: 10px 0;
            line-height: 1.6;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        
        <!-- Slide 1: Executive Summary -->
        <div class="slide">
            <div class="slide-header">
                <div>
                    <h1>Executive Summary</h1>
                    <h2>$30-50B in "Execution Alpha" Value Waiting to be Unlocked</h2>
                </div>
                <div class="cvs-logo">♥ CVS Health</div>
            </div>
            
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">65</div>
                    <div class="metric-label">Investability Score</div>
                    <div class="metric-change negative">3rd Quartile</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">~$40B</div>
                    <div class="metric-label">Execution Alpha</div>
                    <div class="metric-change positive">Value Opportunity</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">7.8×</div>
                    <div class="metric-label">CVS P/E Multiple</div>
                    <div class="metric-change negative">vs UNH 19×</div>
                </div>
            </div>
            
            <div class="chart-container">
                <h3>Enterprise Value Gap Analysis</h3>
                <div class="bar-chart">
                    <div class="bar" style="height: 40%;">
                        <div class="bar-value">$130B</div>
                    </div>
                    <div class="bar" style="height: 60%; background: linear-gradient(to top, #00cc00, #66ff66);">
                        <div class="bar-value">$170B</div>
                    </div>
                </div>
                <div style="display: flex; justify-content: space-around; margin-top: 10px;">
                    <span>Current EV</span>
                    <span>Potential EV</span>
                </div>
            </div>
            
            <div class="priorities-grid">
                <div class="priority-card">
                    <div class="priority-icon">🏥</div>
                    <div class="priority-title">Fix Insurance</div>
                    <div>Restore economics</div>
                </div>
                <div class="priority-card">
                    <div class="priority-icon">🔗</div>
                    <div class="priority-title">Integration</div>
                    <div>Maximize synergies</div>
                </div>
                <div class="priority-card">
                    <div class="priority-icon">👥</div>
                    <div class="priority-title">Customer</div>
                    <div>Enhance experience</div>
                </div>
                <div class="priority-card">
                    <div class="priority-icon">⚙️</div>
                    <div class="priority-title">Operations</div>
                    <div>Drive excellence</div>
                </div>
                <div class="priority-card">
                    <div class="priority-icon">💰</div>
                    <div class="priority-title">Capital</div>
                    <div>Optimize deployment</div>
                </div>
            </div>
            
            <div class="slide-number">1</div>
            <div class="zero-fog-logo">ZERO FOG | powered by rejoyce</div>
        </div>
        
        <!-- Slide 2: Company Overview -->
        <div class="slide">
            <div class="slide-header">
                <div>
                    <h1>Company Overview & Strategic Context</h1>
                    <h2>Healthcare Giant at a Crossroads – Diversified Model, Intensifying Pressure</h2>
                </div>
                <div class="cvs-logo">♥ CVS Health</div>
            </div>
            
            <div style="display: flex; gap: 30px; margin: 30px 0;">
                <div style="flex: 1;">
                    <h3>Scale & Reach</h3>
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-value">185M</div>
                            <div class="metric-label">Consumers Reached</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">9,000+</div>
                            <div class="metric-label">Retail Pharmacies</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">25%</div>
                            <div class="metric-label">US Rx Market Share</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">27M</div>
                            <div class="metric-label">Aetna Members</div>
                        </div>
                    </div>
                </div>
                
                <div style="flex: 1;">
                    <h3>CVS Ecosystem</h3>
                    <div style="display: flex; justify-content: center; align-items: center; height: 200px; background: #f5f5f5; border-radius: 10px;">
                        <div style="text-align: center;">
                            <div style="display: flex; gap: 20px; justify-content: center;">
                                <div style="padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                    <div style="font-size: 36px;">🏪</div>
                                    <div>Retail</div>
                                </div>
                                <div style="padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                    <div style="font-size: 36px;">💊</div>
                                    <div>PBM</div>
                                </div>
                                <div style="padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                    <div style="font-size: 36px;">🏥</div>
                                    <div>Insurance</div>
                                </div>
                                <div style="padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                    <div style="font-size: 36px;">🩺</div>
                                    <div>Clinics</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <h3>Recent Strategic Developments</h3>
            <div class="timeline">
                <div class="timeline-line"></div>
                <div class="timeline-items">
                    <div class="timeline-item">
                        <div class="timeline-date">2024 Q3</div>
                        <div>CEO Change</div>
                        <div style="font-size: 12px;">David Joyner appointed</div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-date">2024 Q4</div>
                        <div>Cost Program</div>
                        <div style="font-size: 12px;">$2B savings launched</div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-date">2025</div>
                        <div>Turnaround Mode</div>
                        <div style="font-size: 12px;">Focus on execution</div>
                    </div>
                </div>
            </div>
            
            <div class="slide-number">2</div>
            <div class="zero-fog-logo">ZERO FOG | powered by rejoyce</div>
        </div>
        
        <!-- Slide 3: Financial Performance -->
        <div class="slide">
            <div class="slide-header">
                <div>
                    <h1>Financial Performance Baseline</h1>
                    <h2>Solid Revenue Growth but Margins & ROIC Lag Peers</h2>
                </div>
                <div class="cvs-logo">♥ CVS Health</div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <div class="chart-container">
                    <h3>Revenue Growth vs Peers</h3>
                    <div class="bar-chart" style="height: 200px;">
                        <div class="bar" style="height: 80%; background: #CC0000;">
                            <div class="bar-value">10%</div>
                            <div class="bar-label">CVS</div>
                        </div>
                        <div class="bar" style="height: 60%; background: #666;">
                            <div class="bar-value">7.5%</div>
                            <div class="bar-label">UNH</div>
                        </div>
                        <div class="bar" style="height: 50%; background: #999;">
                            <div class="bar-value">6%</div>
                            <div class="bar-label">Industry</div>
                        </div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <h3>Operating Margins Comparison</h3>
                    <div class="bar-chart" style="height: 200px;">
                        <div class="bar quartile-3" style="height: 30%; background: #f8d7da;">
                            <div class="bar-value">2.3%</div>
                            <div class="bar-label">CVS</div>
                        </div>
                        <div class="bar quartile-1" style="height: 77%; background: #d4edda;">
                            <div class="bar-value">7.7%</div>
                            <div class="bar-label">UNH</div>
                        </div>
                        <div class="bar" style="height: 50%; background: #666;">
                            <div class="bar-value">5%</div>
                            <div class="bar-label">Peer Median</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="chart-container">
                <h3>ROIC vs WACC Analysis</h3>
                <div style="display: flex; align-items: center; justify-content: center; gap: 50px;">
                    <div class="gauge-chart">
                        <div style="text-align: center;">
                            <div style="font-size: 48px; color: #f44336;">5%</div>
                            <div>ROIC</div>
                        </div>
                    </div>
                    <div style="font-size: 36px;">vs</div>
                    <div class="gauge-chart">
                        <div style="text-align: center;">
                            <div style="font-size: 48px; color: #333;">7-8%</div>
                            <div>WACC</div>
                        </div>
                    </div>
                    <div style="padding: 20px; background: #ffebee; border-radius: 10px;">
                        <strong>Value Destruction</strong><br>
                        Below cost of capital
                    </div>
                </div>
            </div>
            
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Key Metrics</th>
                        <th>CVS</th>
                        <th>UNH</th>
                        <th>Peer Median</th>
                        <th>Quartile</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Revenue CAGR (5yr)</td>
                        <td>7.6%</td>
                        <td>9.2%</td>
                        <td>6.0%</td>
                        <td class="quartile-2">Q2</td>
                    </tr>
                    <tr>
                        <td>EBITDA Margin</td>
                        <td>3.7%</td>
                        <td>8.5%</td>
                        <td>6.0%</td>
                        <td class="quartile-4">Q4</td>
                    </tr>
                    <tr>
                        <td>Net Debt/EBITDA</td>
                        <td>4.5×</td>
                        <td>1.6×</td>
                        <td>2.0×</td>
                        <td class="quartile-4">Q4</td>
                    </tr>
                    <tr>
                        <td>ROE</td>
                        <td>6%</td>
                        <td>25%</td>
                        <td>15%</td>
                        <td class="quartile-4">Q4</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="slide-number">3</div>
            <div class="zero-fog-logo">ZERO FOG | powered by rejoyce</div>
        </div>
        
        <!-- Slide 4: Market & Competitive Benchmarking -->
        <div class="slide">
            <div class="slide-header">
                <div>
                    <h1>Market & Competitive Benchmarking</h1>
                    <h2>Well-Positioned in Market Share, But Customer Metrics Trail Leaders</h2>
                </div>
                <div class="cvs-logo">♥ CVS Health</div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <div>
                    <h3>Market Share Leadership</h3>
                    <div class="chart-container">
                        <div style="display: flex; justify-content: space-around; align-items: flex-end; height: 150px;">
                            <div style="text-align: center;">
                                <div style="width: 100px; height: 100px; background: #CC0000; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold;">25%</div>
                                <div style="margin-top: 10px;">Pharmacy</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="width: 80px; height: 80px; background: #ff6666; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 20px; font-weight: bold;">10%</div>
                                <div style="margin-top: 10px;">Insurance</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3>Customer Experience Gap</h3>
                    <div class="chart-container">
                        <div class="gauge-chart">
                            <div class="gauge">
                                <div class="gauge-fill"></div>
                                <div class="gauge-needle"></div>
                            </div>
                            <div class="gauge-value">NPS: 30-40</div>
                            <div style="text-align: center; color: #666;">vs Best-in-Class: 70</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <h3>Competitive Heatmap</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Metric</th>
                        <th>CVS</th>
                        <th>UNH</th>
                        <th>WBA</th>
                        <th>Cigna</th>
                        <th>Elevance</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Revenue CAGR</td>
                        <td class="quartile-2">7.6%</td>
                        <td class="quartile-1">9.2%</td>
                        <td class="quartile-3">3.1%</td>
                        <td class="quartile-2">7.0%</td>
                        <td class="quartile-2">6.8%</td>
                    </tr>
                    <tr>
                        <td>Operating Margin</td>
                        <td class="quartile-4">2.3%</td>
                        <td class="quartile-1">7.7%</td>
                        <td class="quartile-3">3.5%</td>
                        <td class="quartile-2">5.2%</td>
                        <td class="quartile-2">4.8%</td>
                    </tr>
                    <tr>
                        <td>NPS Score</td>
                        <td class="quartile-3">35</td>
                        <td class="quartile-1">65</td>
                        <td class="quartile-3">30</td>
                        <td class="quartile-2">50</td>
                        <td class="quartile-2">45</td>
                    </tr>
                    <tr>
                        <td>Innovation (% New Rev)</td>
                        <td class="quartile-4">&lt;5%</td>
                        <td class="quartile-1">15%</td>
                        <td class="quartile-3">6%</td>
                        <td class="quartile-2">10%</td>
                        <td class="quartile-2">8%</td>
                    </tr>
                    <tr>
                        <td>Leverage (Debt/EBITDA)</td>
                        <td class="quartile-4">4.5×</td>
                        <td class="quartile-1">1.6×</td>
                        <td class="quartile-3">3.8×</td>
                        <td class="quartile-2">2.5×</td>
                        <td class="quartile-2">2.2×</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="metrics-grid" style="margin-top: 30px;">
                <div class="metric-card">
                    <div class="metric-value">90%</div>
                    <div class="metric-label">Customer Retention</div>
                    <div class="metric-change positive">+1.4M members in 2024</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">$500M+</div>
                    <div class="metric-label">Revenue Opportunity</div>
                    <div class="metric-change positive">Per 1% retention improvement</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">&lt;0.5%</div>
                    <div class="metric-label">R&D Intensity</div>
                    <div class="metric-change negative">vs tech-driven peers ~2%</div>
                </div>
            </div>
            
            <div class="slide-number">4</div>
            <div class="zero-fog-logo">ZERO FOG | powered by rejoyce</div>
        </div>
        
        <!-- Slide 5: Organizational Maturity -->
        <div class="slide">
            <div class="slide-header">
                <div>
                    <h1>Organizational Maturity & Agility</h1>
                    <h2>Mid-Tier Maturity (Joy Score ~3.0) Is Hindering Consistent Performance</h2>
                </div>
                <div class="cvs-logo">♥ CVS Health</div>
            </div>
            
            <div style="display: flex; gap: 40px; align-items: center;">
                <div class="spider-chart">
                    <svg class="spider-svg" viewBox="0 0 400 400">
                        <!-- Grid -->
                        <polygon points="200,80 320,140 320,260 200,320 80,260 80,140" fill="none" stroke="#ddd" stroke-width="1"/>
                        <polygon points="200,120 280,160 280,240 200,280 120,240 120,160" fill="none" stroke="#ddd" stroke-width="1"/>
                        <polygon points="200,160 240,180 240,220 200,240 160,220 160,180" fill="none" stroke="#ddd" stroke-width="1"/>
                        
                        <!-- CVS Score (solid line) -->
                        <polygon points="200,140 260,170 250,230 200,250 140,230 150,170" 
                                fill="rgba(204,0,0,0.2)" stroke="#CC0000" stroke-width="2"/>
                        
                        <!-- Best Practice (dotted line) -->
                        <polygon points="200,100 300,150 300,250 200,300 100,250 100,150" 
                                fill="none" stroke="#4caf50" stroke-width="2" stroke-dasharray="5,5"/>
                        
                        <!-- Labels -->
                        <text x="200" y="70" text-anchor="middle" font-size="14" font-weight="bold">Leadership</text>
                        <text x="340" y="140" text-anchor="start" font-size="14" font-weight="bold">Strategy</text>
                        <text x="340" y="260" text-anchor="start" font-size="14" font-weight="bold">People</text>
                        <text x="200" y="340" text-anchor="middle" font-size="14" font-weight="bold">Process</text>
                        <text x="60" y="260" text-anchor="end" font-size="14" font-weight="bold">Technology</text>
                        <text x="60" y="140" text-anchor="end" font-size="14" font-weight="bold">Metrics</text>
                        
                        <!-- Legend -->
                        <line x1="100" y1="370" x2="130" y2="370" stroke="#CC0000" stroke-width="2"/>
                        <text x="135" y="375" font-size="12">CVS (~3.0)</text>
                        <line x1="220" y1="370" x2="250" y2="370" stroke="#4caf50" stroke-width="2" stroke-dasharray="5,5"/>
                        <text x="255" y="375" font-size="12">Best Practice (~4.5)</text>
                    </svg>
                </div>
                
                <div style="flex: 1;">
                    <h3>Key Maturity Gaps</h3>
                    <div class="bullet-list">
                        <li><strong>People:</strong> High frontline turnover (&gt;20% in retail)</li>
                        <li><strong>Process:</strong> Siloed risk management, slow escalation</li>
                        <li><strong>Metrics:</strong> Inconsistent KPI tracking across units</li>
                        <li><strong>Agility:</strong> Multi-year initiative cycles vs quarterly adaptation</li>
                    </div>
                    
                    <div class="chart-container" style="margin-top: 20px;">
                        <h3>Strategic Velocity Index (SVI)</h3>
                        <div style="display: flex; align-items: center; gap: 20px;">
                            <div style="flex: 1; height: 30px; background: linear-gradient(to right, #f44336, #ff9800, #ffeb3b, #4caf50); border-radius: 15px; position: relative;">
                                <div style="position: absolute; left: 40%; top: -5px; width: 10px; height: 40px; background: #333; border-radius: 5px;"></div>
                            </div>
                            <div>
                                <strong>2.5-3.0 / 5</strong><br>
                                <span style="font-size: 12px;">Moderate Agility</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="chart-container" style="margin-top: 30px;">
                <h3>Impact on Performance</h3>
                <div style="display: flex; align-items: center; justify-content: center; gap: 30px; padding: 20px;">
                    <div style="padding: 15px; background: #ffebee; border-radius: 10px; text-align: center;">
                        <strong>Low Process Maturity</strong>
                    </div>
                    <div style="font-size: 24px;">→</div>
                    <div style="padding: 15px; background: #fff3cd; border-radius: 10px; text-align: center;">
                        <strong>Slow Risk Response</strong>
                    </div>
                    <div style="font-size: 24px;">→</div>
                    <div style="padding: 15px; background: #f8d7da; border-radius: 10px; text-align: center;">
                        <strong>$5B Profit Miss</strong>
                    </div>
                </div>
            </div>
            
            <div class="slide-number">5</div>
            <div class="zero-fog-logo">ZERO FOG | powered by rejoyce</div>
        </div>
        
        <!-- Slide 6: Risk Matrix -->
        <div class="slide">
            <div class="slide-header">
                <div>
                    <h1>Execution Risk & Opportunity Map</h1>
                    <h2>Major Risks Could Leak ~$20B in Value if Unchecked</h2>
                </div>
                <div class="cvs-logo">♥ CVS Health</div>
            </div>
            
            <div class="risk-matrix">
                <div class="axis-label x-axis-label">Likelihood →</div>
                <div class="axis-label y-axis-label">Impact ($B) →</div>
                
                <!-- Insurance Risk -->
                <div class="risk-bubble risk-high" style="width: 120px; height: 120px; top: 50px; right: 150px;">
                    Insurance<br>Volatility<br>$15-20B
                </div>
                
                <!-- Integration Risk -->
                <div class="risk-bubble risk-high" style="width: 100px; height: 100px; top: 80px; right: 200px;">
                    Integration<br>Synergy<br>$5-7B
                </div>
                
                <!-- Retail Transition -->
                <div class="risk-bubble risk-medium" style="width: 80px; height: 80px; top: 200px; right: 80px;">
                    Retail<br>Transition<br>$0.3-0.4B
                </div>
                
                <!-- Regulatory -->
                <div class="risk-bubble risk-high" style="width: 90px; height: 90px; top: 100px; right: 250px;">
                    Regulatory<br>Changes<br>$5B+
                </div>
                
                <!-- Cyber/Tech -->
                <div class="risk-bubble risk-medium" style="width: 70px; height: 70px; top: 220px; right: 220px;">
                    Cyber/<br>Tech<br>$2-3B
                </div>
                
                <!-- Customer Experience -->
                <div class="risk-bubble risk-medium" style="width: 75px; height: 75px; top: 180px; right: 100px;">
                    Customer<br>Churn<br>$0.5B/yr
                </div>
            </div>
            
            <div class="metrics-grid" style="margin-top: 30px;">
                <div class="metric-card">
                    <div class="metric-value">$20B+</div>
                    <div class="metric-label">Total Value at Risk</div>
                    <div class="metric-change negative">Top 2 risks alone</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">$3-5B</div>
                    <div class="metric-label">Annual Leakage</div>
                    <div class="metric-change negative">If insurance volatility continues</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">$500M</div>
                    <div class="metric-label">Annual Synergies</div>
                    <div class="metric-change positive">Target by 2026</div>
                </div>
            </div>
            
            <div class="slide-number">6</div>
            <div class="zero-fog-logo">ZERO FOG | powered by rejoyce</div>
        </div>
        
        <!-- Slide 7: Strategic Lens Analysis -->
        <div class="slide">
            <div class="slide-header">
                <div>
                    <h1>Strategic Lens Scenario Analysis</h1>
                    <h2>Growth Path Offers Highest Long-Term Value (with Execution Demands)</h2>
                </div>
                <div class="cvs-logo">♥ CVS Health</div>
            </div>
            
            <div class="chart-container">
                <h3>Strategic Options Matrix</h3>
                <div style="position: relative; width: 600px; height: 400px; margin: 20px auto; border: 2px solid #333;">
                    <div class="axis-label" style="bottom: -30px; left: 50%; transform: translateX(-50%);">Execution Feasibility →</div>
                    <div class="axis-label" style="left: -100px; top: 50%; transform: translateY(-50%) rotate(-90deg);">Value Potential →</div>
                    
                    <!-- Growth & Expansion -->
                    <div style="position: absolute; top: 50px; left: 250px; width: 150px; height: 150px; background: #CC0000; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; text-align: center;">
                        <div>
                            <strong>Growth &<br>Expansion</strong><br>
                            <span style="font-size: 12px;">+$15-20B Rev<br>Highest EV</span>
                        </div>
                    </div>
                    
                    <!-- Cost Optimization -->
                    <div style="position: absolute; top: 200px; right: 100px; width: 100px; height: 100px; background: #ff9800; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; text-align: center;">
                        <div>
                            <strong>Cost<br>Focus</strong><br>
                            <span style="font-size: 10px;">+50-100bps<br>Quick wins</span>
                        </div>
                    </div>
                    
                    <!-- Innovation -->
                    <div style="position: absolute; top: 100px; left: 100px; width: 110px; height: 110px; background: #9c27b0; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; text-align: center;">
                        <div>
                            <strong>Innovation</strong><br>
                            <span style="font-size: 10px;">3-5yr payoff<br>High uncertainty</span>
                        </div>
                    </div>
                    
                    <!-- Operational Excellence -->
                    <div style="position: absolute; bottom: 50px; right: 50px; width: 90px; height: 90px; background: #607d8b; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; text-align: center;">
                        <div>
                            <strong>Op Ex</strong><br>
                            <span style="font-size: 10px;">Enabler<br>Foundation</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Strategic Lens</th>
                        <th>Revenue CAGR</th>
                        <th>Margin Impact</th>
                        <th>5-Year EV Uplift</th>
                        <th>Execution Risk</th>
                    </tr>
                </thead>
                <tbody>
                    <tr style="background: #ffebee;">
                        <td><strong>Growth & Expansion (Selected)</strong></td>
                        <td>~8%</td>
                        <td>Moderate</td>
                        <td>Highest</td>
                        <td>High</td>
                    </tr>
                    <tr>
                        <td>Cost Optimization</td>
                        <td>~5%</td>
                        <td>+50-100bps</td>
                        <td>Moderate</td>
                        <td>Low</td>
                    </tr>
                    <tr>
                        <td>Innovation Focus</td>
                        <td>~6-7%</td>
                        <td>Long-term</td>
                        <td>High (3-5yr)</td>
                        <td>Very High</td>
                    </tr>
                    <tr>
                        <td>Operational Excellence</td>
                        <td>~5%</td>
                        <td>+30-50bps</td>
                        <td>Low-Moderate</td>
                        <td>Low</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="slide-number">7</div>
            <div class="zero-fog-logo">ZERO FOG | powered by rejoyce</div>
        </div>
        
        <!-- Slide 8: Strategic Priorities Overview -->
        <div class="slide">
            <div class="slide-header">
                <div>
                    <h1>Strategic Priorities Overview</h1>
                    <h2>Five Execution Priorities to Unlock CVS's Growth & Value</h2>
                </div>
                <div class="cvs-logo">♥ CVS Health</div>
            </div>
            
            <div class="priorities-grid" style="grid-template-columns: repeat(5, 1fr);">
                <div class="priority-card" style="background: linear-gradient(135deg, #ffebee, #ef5350);">
                    <div class="priority-icon">🏥</div>
                    <div class="priority-title">Priority 1</div>
                    <div><strong>Restore Insurance Economics</strong></div>
                    <div style="font-size: 12px; margin-top: 10px;">
                        • Fix MBR to ~85%<br>
                        • +$1B per 1% improvement<br>
                        • SWAT team deployment
                    </div>
                </div>
                
                <div class="priority-card" style="background: linear-gradient(135deg, #e3f2fd, #2196f3);">
                    <div class="priority-icon">🔗</div>
                    <div class="priority-title">Priority 2</div>
                    <div><strong>Maximize Integration</strong></div>
                    <div style="font-size: 12px; margin-top: 10px;">
                        • $500M synergies by 2026<br>
                        • Cross-business programs<br>
                        • Integration PMO
                    </div>
                </div>
                
                <div class="priority-card" style="background: linear-gradient(135deg, #f3e5f5, #9c27b0);">
                    <div class="priority-icon">👥</div>
                    <div class="priority-title">Priority 3</div>
                    <div><strong>Enhance Customer Experience</strong></div>
                    <div style="font-size: 12px; margin-top: 10px;">
                        • +5 NPS points<br>
                        • Unified digital platform<br>
                        • $500M from retention
                    </div>
                </div>
                
                <div class="priority-card" style="background: linear-gradient(135deg, #e8f5e9, #4caf50);">
                    <div class="priority-icon">⚙️</div>
                    <div class="priority-title">Priority 4</div>
                    <div><strong>Operational Excellence</strong></div>
                    <div style="font-size: 12px; margin-top: 10px;">
                        • +$1B cost savings<br>
                        • Automation & lean<br>
                        • Six Sigma quality
                    </div>
                </div>
                
                <div class="priority-card" style="background: linear-gradient(135deg, #fff3e0, #ff9800);">
                    <div class="priority-icon">💰</div>
                    <div class="priority-title">Priority 5</div>
                    <div><strong>Capital Optimization</strong></div>
                    <div style="font-size: 12px; margin-top: 10px;">
                        • Delever to <3× by 2025<br>
                        • ROIC > WACC focus<br>
                        • Portfolio review
                    </div>
                </div>
            </div>
            
            <div class="chart-container" style="margin-top: 30px;">
                <h3>Implementation Approach</h3>
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px;">
                    <div style="text-align: center;">
                        <div style="font-size: 36px;">🚀</div>
                        <strong>Quick Wins</strong><br>
                        <span style="font-size: 12px;">0-100 days</span>
                    </div>
                    <div style="font-size: 24px;">→</div>
                    <div style="text-align: center;">
                        <div style="font-size: 36px;">🔧</div>
                        <strong>Structural Changes</strong><br>
                        <span style="font-size: 12px;">6-12 months</span>
                    </div>
                    <div style="font-size: 24px;">→</div>
                    <div style="text-align: center;">
                        <div style="font-size: 36px;">📈</div>
                        <strong>Sustained Impact</strong><br>
                        <span style="font-size: 12px;">12-24 months</span>
                    </div>
                </div>
            </div>
            
            <div class="slide-number">8</div>
            <div class="zero-fog-logo">ZERO FOG | powered by rejoyce</div>
        </div>
        
        <!-- Slide 9: Priority 1 - Restore Insurance Economics -->
        <div class="slide">
            <div class="slide-header">
                <div>
                    <h1>Priority 1: Restore Insurance Segment Economics</h1>
                    <h2>Aetna's $5B Profit Miss Exposed an Execution Gap – Closing It Can Reclaim ~$1B+ Annual Profit</h2>
                </div>
                <div class="cvs-logo">♥ CVS Health</div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <div class="chart-container">
                    <h3>Medical Benefit Ratio Gap</h3>
                    <div class="bar-chart" style="height: 250px;">
                        <div class="bar" style="height: 92.5%; background: #f44336;">
                            <div class="bar-value">92.5%</div>
                            <div class="bar-label">2024 Actual</div>
                        </div>
                        <div class="bar" style="height: 85%; background: #4caf50;">
                            <div class="bar-value">~85%</div>
                            <div class="bar-label">Target/Peers</div>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 10px; font-weight: bold; color: #CC0000;">
                        Each 1% = $1B+ Profit
                    </div>
                </div>
                
                <div>
                    <h3>Insurance SWAT Team Structure</h3>
                    <div style="background: #f5f5f5; padding: 20px; border-radius: 10px;">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <div style="font-size: 36px;">🚨</div>
                            <strong>Cross-Functional SWAT Team</strong>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <div style="background: white; padding: 10px; border-radius: 5px;">
                                <strong>📊 Actuaries</strong><br>
                                <span style="font-size: 12px;">Pricing & reserves</span>
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 5px;">
                                <strong>🩺 Clinicians</strong><br>
                                <span style="font-size: 12px;">Care management</span>
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 5px;">
                                <strong>📈 Data Scientists</strong><br>
                                <span style="font-size: 12px;">Trend detection</span>
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 5px;">
                                <strong>⚙️ Operators</strong><br>
                                <span style="font-size: 12px;">Rapid response</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <h3>Key Actions & Timeline</h3>
            <div class="timeline">
                <div class="timeline-line"></div>
                <div class="timeline-items">
                    <div class="timeline-item">
                        <div class="timeline-date">Immediate</div>
                        <div><strong>Pricing Discipline</strong></div>
                        <ul style="text-align: left; font-size: 12px; list-style: none; padding: 0;">
                            <li>• Reprice 2025-26 products</li>
                            <li>• Add 5-7% cost buffer</li>
                            <li>• Mid-year triggers</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-date">0-3 Months</div>
                        <div><strong>Quick Wins</strong></div>
                        <ul style="text-align: left; font-size: 12px; list-style: none; padding: 0;">
                            <li>• Target high-cost cohorts</li>
                            <li>• CVS asset interventions</li>
                            <li>• Consider reinsurance</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-date">6-12 Months</div>
                        <div><strong>Structural Fixes</strong></div>
                        <ul style="text-align: left; font-size: 12px; list-style: none; padding: 0;">
                            <li>• Medicare 4+ stars</li>
                            <li>• Risk partnerships</li>
                            <li>• Org restructure</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="metrics-grid" style="margin-top: 30px;">
                <div class="metric-card">
                    <div class="metric-value">$5.3B</div>
                    <div class="metric-label">2024 Profit Miss</div>
                    <div class="metric-change negative">Must not repeat</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">Weekly</div>
                    <div class="metric-label">SWAT Monitoring</div>
                    <div class="metric-change positive">Real-time dashboards</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">4.0★</div>
                    <div class="metric-label">Target Star Rating</div>
                    <div class="metric-change positive">Revenue boost</div>
                </div>
            </div>
            
            <div class="slide-number">9</div>
            <div class="zero-fog-logo">ZERO FOG | powered by rejoyce</div>
        </div>
        
        <!-- Slide 10: Priority 2 - Maximize Integration Synergies -->
        <div class="slide">
            <div class="slide-header">
                <div>
                    <h1>Priority 2: Maximize Integration Synergies</h1>
                    <h2>CVS's Integrated Model Promises '1+1=3' – We Need to Execute It, or Risk $5B+ in Lost Value</h2>
                </div>
                <div class="cvs-logo">♥ CVS Health</div>
            </div>
            
            <div class="chart-container">
                <h3>Integrated Ecosystem Value Creation</h3>
                <div style="display: flex; justify-content: center; align-items: center; padding: 20px;">
                    <svg width="500" height="300" viewBox="0 0 500 300">
                        <!-- Central hub -->
                        <circle cx="250" cy="150" r="40" fill="#CC0000" opacity="0.2" stroke="#CC0000" stroke-width="2"/>
                        <text x="250" y="155" text-anchor="middle" font-weight="bold">MEMBER</text>
                        
                        <!-- Aetna Insurance -->
                        <circle cx="150" cy="80" r="35" fill="#2196f3" opacity="0.3" stroke="#2196f3" stroke-width="2"/>
                        <text x="150" y="85" text-anchor="middle" font-size="12">Aetna</text>
                        
                        <!-- CVS Pharmacy -->
                        <circle cx="350" cy="80" r="35" fill="#4caf50" opacity="0.3" stroke="#4caf50" stroke-width="2"/>
                        <text x="350" y="85" text-anchor="middle" font-size="12">Pharmacy</text>
                        
                        <!-- MinuteClinic/Oak -->
                        <circle cx="150" cy="220" r="35" fill="#ff9800" opacity="0.3" stroke="#ff9800" stroke-width="2"/>
                        <text x="150" y="225" text-anchor="middle" font-size="12">Clinics</text>
                        
                        <!-- Caremark PBM -->
                        <circle cx="350" cy="220" r="35" fill="#9c27b0" opacity="0.3" stroke="#9c27b0" stroke-width="2"/>
                        <text x="350" y="225" text-anchor="middle" font-size="12">PBM</text>
                        
                        <!-- Connection arrows with synergy values -->
                        <line x1="180" y1="100" x2="220" y2="130" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
                        <text x="200" y="110" font-size="10">$0 copay</text>
                        
                        <line x1="320" y1="100" x2="280" y2="130" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
                        <text x="300" y="110" font-size="10">Discounts</text>
                        
                        <line x1="180" y1="200" x2="220" y2="170" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
                        <text x="200" y="190" font-size="10">Care coord</text>
                        
                        <line x1="320" y1="200" x2="280" y2="170" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
                        <text x="300" y="190" font-size="10">Data alerts</text>
                        
                        <!-- Arrow marker definition -->
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
            </div>
            
            <h3>Integration Synergy Programs</h3>
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;">
                <div style="background: #e3f2fd; padding: 15px; border-radius: 10px;">
                    <strong>CVS OneCare</strong>
                    <ul style="font-size: 12px; margin-top: 10px;">
                        <li>$0 copay for Aetna at CVS clinics</li>
                        <li>Priority same-day appointments</li>
                        <li>Pharmacy rewards program</li>
                    </ul>
                </div>
                <div style="background: #f3e5f5; padding: 15px; border-radius: 10px;">
                    <strong>Chronic Care Collab</strong>
                    <ul style="font-size: 12px; margin-top: 10px;">
                        <li>Pharmacist-physician co-manage</li>
                        <li>Medication adherence tracking</li>
                        <li>Preventive care coordination</li>
                    </ul>
                </div>
                <div style="background: #e8f5e9; padding: 15px; border-radius: 10px;">
                    <strong>PBM-Clinical Link</strong>
                    <ul style="font-size: 12px; margin-top: 10px;">
                        <li>Adherence alerts & outreach</li>
                        <li>High-risk patient flagging</li>
                        <li>Proactive interventions</li>
                    </ul>
                </div>
            </div>
            
            <div class="chart-container" style="margin-top: 30px;">
                <h3>$500M Synergy Target Breakdown by 2026</h3>
                <div class="bar-chart" style="height: 200px;">
                    <div class="bar" style="height: 40%; background: #2196f3;">
                        <div class="bar-value">$200M</div>
                        <div class="bar-label">Reduced Claims</div>
                    </div>
                    <div class="bar" style="height: 20%; background: #4caf50;">
                        <div class="bar-value">$100M</div>
                        <div class="bar-label">Adherence</div>
                    </div>
                    <div class="bar" style="height: 40%; background: #ff9800;">
                        <div class="bar-value">$200M</div>
                        <div class="bar-label">Cross-sell/Retention</div>
                    </div>
                </div>
            </div>
            
            <div class="slide-number">10</div>
            <div class="zero-fog-logo">ZERO FOG | powered by rejoyce</div>
        </div>
        
        <!-- Slide 11: Priority 3 - Enhance Customer Experience -->
        <div class="slide">
            <div class="slide-header">
                <div>
                    <h1>Priority 3: Enhance Customer Experience</h1>
                    <h2>Fragmented Experience Is Costing CVS Customer Loyalty – A Unified Approach Can Raise NPS by 5+ Points</h2>
                </div>
                <div class="cvs-logo">♥ CVS Health</div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <div>
                    <h3>Current State: Fragmented</h3>
                    <div style="background: #ffebee; padding: 20px; border-radius: 10px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                            <div style="background: white; padding: 10px; border-radius: 5px; text-align: center;">
                                📱 Pharmacy App
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 5px; text-align: center;">
                                📱 Aetna App
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 5px; text-align: center;">
                                📱 MinuteClinic App
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 5px; text-align: center;">
                                📱 ExtraCare App
                            </div>
                        </div>
                        <div style="text-align: center; color: #d32f2f;">
                            <strong>Result:</strong> Confusion & Frustration
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3>Future State: Unified</h3>
                    <div style="background: #e8f5e9; padding: 20px; border-radius: 10px;">
                        <div style="background: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 15px;">
                            <div style="font-size: 48px;">📲</div>
                            <strong>One CVS Health App</strong>
                            <div style="font-size: 12px; margin-top: 10px;">
                                All services • Single login • Personalized
                            </div>
                        </div>
                        <div style="text-align: center; color: #2e7d32;">
                            <strong>Result:</strong> Simplicity & Engagement
                        </div>
                    </div>
                </div>
            </div>
            
            <h3>Key Digital Features</h3>
            <div class="priorities-grid" style="grid-template-columns: repeat(4, 1fr);">
                <div class="priority-card">
                    <div style="font-size: 36px;">💊</div>
                    <div><strong>Real-time Rx Status</strong></div>
                    <div style="font-size: 11px;">Track prescriptions & copays</div>
                </div>
                <div class="priority-card">
                    <div style="font-size: 36px;">📅</div>
                    <div><strong>One-Click Booking</strong></div>
                    <div style="font-size: 11px;">Schedule any CVS service</div>
                </div>
                <div class="priority-card">
                    <div style="font-size: 36px;">🎯</div>
                    <div><strong>Personalized Alerts</strong></div>
                    <div style="font-size: 11px;">Health reminders & offers</div>
                </div>
                <div class="priority-card">
                    <div style="font-size: 36px;">🏆</div>
                    <div><strong>Health Rewards</strong></div>
                    <div style="font-size: 11px;">Earn for healthy behaviors</div>
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div class="chart-container">
                    <h3>NPS Improvement Target</h3>
                    <div style="display: flex; align-items: center; justify-content: center; gap: 30px;">
                        <div style="text-align: center;">
                            <div style="font-size: 48px; color: #f44336;">35</div>
                            <div>Current NPS</div>
                        </div>
                        <div style="font-size: 36px;">→</div>
                        <div style="text-align: center;">
                            <div style="font-size: 48px; color: #4caf50;">40+</div>
                            <div>Target NPS</div>
                        </div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <h3>Financial Impact</h3>
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-value">+1%</div>
                            <div class="metric-label">Retention Improvement</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">$500M</div>
                            <div class="metric-label">Annual Revenue Impact</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="slide-number">11</div>
            <div class="zero-fog-logo">ZERO FOG | powered by rejoyce</div>
        </div>
        
        <!-- Slide 12: Priority 4 - Operational Excellence -->
        <div class="slide">
            <div class="slide-header">
                <div>
                    <h1>Priority 4: Drive Operational Excellence</h1>
                    <h2>CVS Runs a Tight Ship in Areas, But Efficiency Gaps Can Free >$1B for Reinvestment</h2>
                </div>
                <div class="cvs-logo">♥ CVS Health</div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <div class="chart-container">
                    <h3>Cost Savings Waterfall</h3>
                    <div style="display: flex; align-items: flex-end; gap: 5px; height: 250px;">
                        <div style="background: #666; color: white; padding: 10px; height: 100%; display: flex; align-items: center; justify-content: center; border-radius: 5px;">
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: bold;">$2B</div>
                                <div style="font-size: 12px;">Current Program</div>
                            </div>
                        </div>
                        <div style="font-size: 24px; align-self: center;">+</div>
                        <div style="background: #4caf50; color: white; padding: 10px; height: 30%; border-radius: 5px;">
                            <div style="text-align: center;">
                                <div>$300M</div>
                                <div style="font-size: 10px;">Vendor</div>
                            </div>
                        </div>
                        <div style="background: #2196f3; color: white; padding: 10px; height: 20%; border-radius: 5px;">
                            <div style="text-align: center;">
                                <div>$200M</div>
                                <div style="font-size: 10px;">Labor</div>
                            </div>
                        </div>
                        <div style="background: #ff9800; color: white; padding: 10px; height: 50%; border-radius: 5px;">
                            <div style="text-align: center;">
                                <div>$500M</div>
                                <div style="font-size: 10px;">Automation</div>
                            </div>
                        </div>
                        <div style="font-size: 24px; align-self: center;">=</div>
                        <div style="background: #CC0000; color: white; padding: 10px; height: 100%; display: flex; align-items: center; justify-content: center; border-radius: 5px;">
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: bold;">$3B+</div>
                                <div style="font-size: 12px;">Total Target</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3>Key Efficiency Initiatives</h3>
                    <div style="display: grid; gap: 10px;">
                        <div style="background: #f5f5f5; padding: 15px; border-left: 4px solid #4caf50; border-radius: 4px;">
                            <strong>🏪 Store Optimization</strong>
                            <div style="font-size: 12px;">900+ closures, health hub investment</div>
                        </div>
                        <div style="background: #f5f5f5; padding: 15px; border-left: 4px solid #2196f3; border-radius: 4px;">
                            <strong>🤖 Automation Scale-up</strong>
                            <div style="font-size: 12px;">Central fill, AI prior auth, chatbots</div>
                        </div>
                        <div style="background: #f5f5f5; padding: 15px; border-left: 4px solid #ff9800; border-radius: 4px;">
                            <strong>📊 Lean Six Sigma</strong>
                            <div style="font-size: 12px;">Monthly Kaizen, <3.4 defects/million</div>
                        </div>
                        <div style="background: #f5f5f5; padding: 15px; border-left: 4px solid #9c27b0; border-radius: 4px;">
                            <strong>📈 SG&A Excellence</strong>
                            <div style="font-size: 12px;">11.5% → 10.5% of revenue target</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <h3>Operational Excellence Metrics</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Metric</th>
                        <th>Current</th>
                        <th>Target</th>
                        <th>Impact</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>SG&A % of Revenue</td>
                        <td>11.5%</td>
                        <td>10.5%</td>
                        <td>~$3.7B opportunity</td>
                    </tr>
                    <tr>
                        <td>Rx Accuracy</td>
                        <td>99.95%</td>
                        <td>99.99%</td>
                        <td>Six Sigma quality</td>
                    </tr>
                    <tr>
                        <td>Scripts per Pharmacist</td>
                        <td>Baseline</td>
                        <td>+20%</td>
                        <td>Via central fill</td>
                    </tr>
                    <tr>
                        <td>Process Cycle Time</td>
                        <td>Baseline</td>
                        <td>-30%</td>
                        <td>Customer satisfaction</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="slide-number">12</div>
            <div class="zero-fog-logo">ZERO FOG | powered by rejoyce</div>
        </div>
        
        <!-- Slide 13: Priority 5 - Capital Optimization -->
        <div class="slide">
            <div class="slide-header">
                <div>
                    <h1>Priority 5: Optimize Capital Deployment</h1>
                    <h2>CVS's Capital Must Work Harder – Deleveraging Can Raise ROIC Above WACC</h2>
                </div>
                <div class="cvs-logo">♥ CVS Health</div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <div class="chart-container">
                    <h3>Deleveraging Trajectory</h3>
                    <div style="position: relative; height: 250px;">
                        <svg width="100%" height="100%" viewBox="0 0 400 250">
                            <!-- Grid lines -->
                            <line x1="50" y1="200" x2="350" y2="200" stroke="#ddd" stroke-width="1"/>
                            <line x1="50" y1="150" x2="350" y2="150" stroke="#ddd" stroke-width="1"/>
                            <line x1="50" y1="100" x2="350" y2="100" stroke="#ddd" stroke-width="1"/>
                            <line x1="50" y1="50" x2="350" y2="50" stroke="#ddd" stroke-width="1"/>
                            
                            <!-- Axes -->
                            <line x1="50" y1="20" x2="50" y2="200" stroke="#333" stroke-width="2"/>
                            <line x1="50" y1="200" x2="350" y2="200" stroke="#333" stroke-width="2"/>
                            
                            <!-- Leverage line -->
                            <polyline points="100,50 200,100 300,150" fill="none" stroke="#CC0000" stroke-width="3"/>
                            
                            <!-- Target line -->
                            <line x1="50" y1="150" x2="350" y2="150" stroke="#4caf50" stroke-width="2" stroke-dasharray="5,5"/>
                            
                            <!-- Labels -->
                            <text x="30" y="55" font-size="12">4.5×</text>
                            <text x="30" y="105" font-size="12">3.5×</text>
                            <text x="30" y="155" font-size="12">3.0×</text>
                            <text x="100" y="220" font-size="12">2024</text>
                            <text x="200" y="220" font-size="12">2025</text>
                            <text x="300" y="220" font-size="12">2026</text>
                            <text x="360" y="155" font-size="10" fill="#4caf50">Target</text>
                        </svg>
                    </div>
                </div>
                
                <div class="chart-container">
                    <h3>ROIC vs WACC Progress</h3>
                    <div style="display: flex; justify-content: center; align-items: center; height: 250px;">
                        <div style="text-align: center;">
                            <div style="display: flex; gap: 30px; align-items: center;">
                                <div>
                                    <div style="font-size: 36px; color: #f44336;">5%</div>
                                    <div>Current ROIC</div>
                                </div>
                                <div style="font-size: 24px;">→</div>
                                <div>
                                    <div style="font-size: 36px; color: #ff9800;">7-8%</div>
                                    <div>WACC</div>
                                </div>
                                <div style="font-size: 24px;">→</div>
                                <div>
                                    <div style="font-size: 36px; color: #4caf50;">10%+</div>
                                    <div>Target ROIC</div>
                                </div>
                            </div>
                            <div style="margin-top: 20px; padding: 10px; background: #e8f5e9; border-radius: 5px;">
                                <strong>Value Creation Mode</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <h3>Capital Allocation Framework</h3>
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin: 30px 0;">
                <div style="background: linear-gradient(135deg, #ffebee, #f44336); padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 36px;">💸</div>
                    <div><strong>Debt Paydown</strong></div>
                    <div style="font-size: 12px;">$15B by 2025</div>
                </div>
                <div style="background: linear-gradient(135deg, #fff3e0, #ff9800); padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 36px;">📊</div>
                    <div><strong>ROIC > WACC</strong></div>
                    <div style="font-size: 12px;">Hurdle rate 12%</div>
                </div>
                <div style="background: linear-gradient(135deg, #e3f2fd, #2196f3); padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 36px;">🎯</div>
                    <div><strong>Portfolio Review</strong></div>
                    <div style="font-size: 12px;">Non-core exits</div>
                </div>
                <div style="background: linear-gradient(135deg, #e8f5e9, #4caf50); padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 36px;">💰</div>
                    <div><strong>Dividend Maintain</strong></div>
                    <div style="font-size: 12px;">Buybacks on hold</div>
                </div>
            </div>
            
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">$60B</div>
                    <div class="metric-label">Current Net Debt</div>
                    <div class="metric-change negative">4.5× EBITDA</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">$15B</div>
                    <div class="metric-label">Debt Reduction Target</div>
                    <div class="metric-change positive">By end 2025</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value"><3×</div>
                    <div class="metric-label">Target Leverage</div>
                    <div class="metric-change positive">2025-26</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">8%</div>
                    <div class="metric-label">FCF Yield</div>
                    <div class="metric-change positive">Strong cash generation</div>
                </div>
            </div>
            
            <div class="slide-number">13</div>
            <div class="zero-fog-logo">ZERO FOG | powered by rejoyce</div>
        </div>
        
        <!-- Slide 14: 100-Day Plan -->
        <div class="slide">
            <div class="slide-header">
                <div>
                    <h1>100-Day Plan</h1>
                    <h2>Mobilizing Quick Wins and Momentum</h2>
                </div>
                <div class="cvs-logo">♥ CVS Health</div>
            </div>
            
            <div class="timeline" style="margin-top: 40px;">
                <div class="timeline-line"></div>
                <div class="timeline-items">
                    <div class="timeline-item">
                        <div class="timeline-date">Days 0-30</div>
                        <div style="font-weight: bold; margin: 10px 0;">Initiate & Announce</div>
                        <ul style="text-align: left; font-size: 12px; list-style: none; padding: 0;">
                            <li>✓ Launch Insurance SWAT Team</li>
                            <li>✓ Stand up Integration PMO</li>
                            <li>✓ Start 3-market pilot</li>
                            <li>✓ Identify $300M quick saves</li>
                            <li>✓ Communicate capital plan</li>
                        </ul>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-date">Days 31-60</div>
                        <div style="font-weight: bold; margin: 10px 0;">Implement & Build</div>
                        <ul style="text-align: left; font-size: 12px; list-style: none; padding: 0;">
                            <li>✓ Deploy feedback dashboard</li>
                            <li>✓ Beta unified app</li>
                            <li>✓ New staffing models</li>
                            <li>✓ First Kaizen events</li>
                            <li>✓ Finalize 2025 pricing</li>
                        </ul>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-date">Days 61-100</div>
                        <div style="font-weight: bold; margin: 10px 0;">Review & Expand</div>
                        <ul style="text-align: left; font-size: 12px; list-style: none; padding: 0;">
                            <li>✓ Pilot results review</li>
                            <li>✓ Lock additional $1B saves</li>
                            <li>✓ Board update session</li>
                            <li>✓ Fill key talent gaps</li>
                            <li>✓ Publicize early wins</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="metrics-grid" style="margin-top: 40px;">
                <div class="metric-card">
                    <div class="metric-value">5</div>
                    <div class="metric-label">Key Task Forces</div>
                    <div class="metric-change positive">Launched Day 1</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">3</div>
                    <div class="metric-label">Pilot Markets</div>
                    <div class="metric-change positive">Integration testing</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">$300M</div>
                    <div class="metric-label">Quick Savings</div>
                    <div class="metric-change positive">Identified Month 1</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">100%</div>
                    <div class="metric-label">Leadership Aligned</div>
                    <div class="metric-change positive">Incentives reset</div>
                </div>
            </div>
            
            <div class="chart-container" style="margin-top: 30px;">
                <h3>Expected Outcomes by Day 100</h3>
                <ul class="bullet-list">
                    <li><strong>Insurance:</strong> SWAT team operational, first interventions showing impact</li>
                    <li><strong>Integration:</strong> Pilot results demonstrate $X million annualized synergy potential</li>
                    <li><strong>Customer:</strong> Beta app launched, NPS tracking improvements in pilot markets</li>
                    <li><strong>Operations:</strong> $300M+ cost savings locked, lean culture taking hold</li>
                    <li><strong>Capital:</strong> Debt reduction on track, clear communication to investors</li>
                </ul>
            </div>
            
            <div class="slide-number">14</div>
            <div class="zero-fog-logo">ZERO FOG | powered by rejoyce</div>
        </div>
        
    </div>
</body>
</html>