<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CVS Health - Enterprise Readiness Assessment Report</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        html {
            scroll-behavior: smooth;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f2f5;
        }
        .page-container {
            max-width: 1200px;
            margin: 2rem auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .page {
            padding: 4rem;
            min-height: 100vh; /* Approximate A4 ratio */
            border-bottom: 1px solid #e5e7eb;
            position: relative;
        }
         .page:last-child {
            border-bottom: none;
        }
        .footer {
            position: absolute;
            bottom: 2rem;
            left: 4rem;
            right: 4rem;
            text-align: center;
            font-size: 0.875rem;
            color: #6b7280;
        }
        h1, h2, h3, h4 {
            font-weight: 700;
            color: #1a2a4d;
        }
        h1 { font-size: 2.25rem; }
        h2 { font-size: 1.75rem; border-bottom: 2px solid #e11d48; padding-bottom: 0.5rem; margin-top: 2rem; margin-bottom: 1.5rem; }
        h3 { font-size: 1.25rem; font-weight: 600; margin-top: 1.5rem; margin-bottom: 1rem; color: #374151;}
        p, ul, ol {
            color: #4b5563;
            line-height: 1.7;
            margin-bottom: 1rem;
        }
        ul, ol {
            list-style-position: inside;
            padding-left: 1rem;
        }
        li {
            margin-bottom: 0.5rem;
        }
        .cvs-red { color: #e11d48; }
        .cvs-red-bg { background-color: #e11d48; }
        .text-risk { color: #dc2626; }
        .text-opportunity { color: #16a34a; }

        .cover-page {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            background: linear-gradient(rgba(255,255,255,0.9), rgba(255,255,255,0.9)), url('https://placehold.co/1200x1600/e0e7ff/e0e7ff?text=+');
            background-size: cover;
        }
        .cover-page .logo {
            width: 150px;
            height: auto;
            margin-bottom: 2rem;
        }
        .cover-page h1 {
             font-size: 3rem;
             color: #1a2a4d;
        }
        .cover-page .subtitle {
            font-size: 1.5rem;
            color: #374151;
            margin-top: 1rem;
        }
        .cover-page .tagline {
            font-size: 1.125rem;
            color: #6b7280;
            margin-top: 2rem;
        }
        .cover-page .date-line {
            position: absolute;
            bottom: 4rem;
            font-size: 1rem;
            color: #4b5563;
        }
        .toc-list {
            list-style-type: none;
            padding-left: 0;
        }
        .toc-list li {
            font-size: 1.125rem;
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
        }
        .toc-list a {
            color: #1a2a4d;
            text-decoration: none;
            transition: color 0.3s;
        }
        .toc-list a:hover {
            color: #e11d48;
        }
        .toc-list .dots {
            flex-grow: 1;
            border-bottom: 1px dotted #9ca3af;
            margin: 0 0.5rem;
            position: relative;
            bottom: 4px;
        }

        .kpi-card {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 0.75rem;
            padding: 1.5rem;
            text-align: center;
        }
        .kpi-card-title {
            font-weight: 600;
            color: #374151;
        }
        .heatmap-table {
            width: 100%;
            border-collapse: collapse;
        }
        .heatmap-table th, .heatmap-table td {
            border: 1px solid #e5e7eb;
            padding: 0.75rem;
            text-align: left;
        }
        .heatmap-table th {
            background-color: #f3f4f6;
        }
        .q1 { background-color: #dcfce7; } /* Top Quartile */
        .q2 { background-color: #fef9c3; } /* Mid-High */
        .q3 { background-color: #fee2e2; } /* Mid-Low */
        .q4 { background-color: #fecaca; } /* Bottom Quartile */
        .highlight-col {
            background-color: #eff6ff;
            font-weight: 600;
        }
        .priority-box {
            border-left: 4px solid #e11d48;
            padding-left: 1.5rem;
            margin-bottom: 2rem;
        }
        .priority-box h4 {
            font-size: 1.1rem;
            font-weight: 700;
            margin-top: 0;
            color: #1a2a4d;
        }
        .roadmap-phase {
            background-color: #f9fafb;
            border-radius: 0.5rem;
            padding: 1.5rem;
        }
        .roadmap-phase h4 {
            color: #e11d48;
            border-bottom: 2px solid #f3f4f6;
            padding-bottom: 0.5rem;
            margin-bottom: 1rem;
        }
        .glossary dt {
            font-weight: 600;
            color: #374151;
        }
        .glossary dd {
            margin-left: 1.5rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- Cover Page -->
        <div class="page cover-page" id="cover">
            <img src="https://placehold.co/150x50/e11d48/ffffff?text=CVS+Health" alt="CVS Health Logo" class="logo">
            <h1>Enterprise Readiness Assessment (ERA) Report</h1>
            <p class="subtitle">CVS Health – Execution & Value Creation Analysis (YTD 2025)</p>
            <p class="tagline">Quantifying Execution Maturity, Agility, and Enterprise Value Impact</p>
            <div class="date-line">
                August 2025 | Confidential Draft v1.0
            </div>
        </div>
        
        <!-- Table of Contents -->
        <div class="page" id="toc">
            <h2>Table of Contents</h2>
            <ol class="toc-list">
                <li><a href="#executive-summary">Executive Summary</a><span class="dots"></span><span>3</span></li>
                <li><a href="#overview">Company Overview & Strategic Context</a><span class="dots"></span><span>4</span></li>
                <li><a href="#financials">Financial Performance Baseline</a><span class="dots"></span><span>5</span></li>
                <li><a href="#benchmarking">Market & Competitive Benchmarking</a><span class="dots"></span><span>6</span></li>
                <li><a href="#maturity">Organizational Maturity & Execution Agility</a><span class="dots"></span><span>7</span></li>
                <li><a href="#strategic-lenses">Strategic Lens Alternatives & Scenario Analysis</a><span class="dots"></span><span>8</span></li>
                <li><a href="#risk">Execution Risk & Opportunity Mapping</a><span class="dots"></span><span>9</span></li>
                <li><a href="#recommendations">Strategic Recommendations</a><span class="dots"></span><span>10</span></li>
                <li><a href="#roadmap">100-Day Plan (Directional)</a><span class="dots"></span><span>11</span></li>
                <li><a href="#appendices">Appendices</a><span class="dots"></span><span>12</span></li>
            </ol>
             <div class="footer">Page 2 | Confidential</div>
        </div>

        <!-- Executive Summary -->
        <div class="page" id="executive-summary">
            <h2>Executive Summary</h2>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div class="lg:col-span-2">
                    <h3>Investability & Strategic Outlook</h3>
                    <p>CVS Health’s <strong>Investability Score</strong> is currently estimated in the mid-60s on a 100-point scale, placing CVS in the <strong>3rd quartile among peers</strong>. This reflects a robust revenue base and scale advantage, yet below-par profitability and high leverage have dampened investor confidence. The proprietary <strong>Execution Alpha Index</strong> reveals an estimated <strong>$30–50 billion in “trapped” enterprise value</strong> that CVS could unlock by closing performance gaps.</p>
                    
                    <h3>Top Strategic Imperatives</h3>
                    <ul class="list-disc">
                        <li><strong>Integrate & Optimize New Care Assets:</strong> Accelerate integration of Oak Street Health and Signify Health.</li>
                        <li><strong>Reinforce Core Profitability:</strong> Address margin pressures in the Health Care Benefits segment.</li>
                        <li><strong>Innovate the Consumer Experience:</strong> Enhance digital health services and personalized engagement.</li>
                        <li><strong>Increase Organizational Agility:</strong> Invest in talent, culture, and technology to improve execution speed.</li>
                        <li><strong>Disciplined Capital & Portfolio Management:</strong> Continue deleveraging and evaluate strategic focus.</li>
                    </ul>

                     <h3>Key Risks & Opportunities</h3>
                    <p><strong class="text-opportunity">Opportunities</strong> include an aging population, synergies from vertical integration, and share gains as weaker rivals retrench. <strong class="text-risk">Risks</strong> center on execution, including integration missteps, high medical cost trends, and competitive threats from both traditional and tech-based entrants.</p>

                </div>
                <div class="space-y-6">
                    <div class="kpi-card">
                        <h4 class="kpi-card-title">Investability Score</h4>
                        <canvas id="investabilityGauge"></canvas>
                        <p class="text-sm text-gray-500 mt-2">3rd Quartile vs. Peers</p>
                    </div>
                    <div class="kpi-card">
                        <h4 class="kpi-card-title">Execution Alpha (Value at Stake)</h4>
                        <canvas id="executionAlphaChart"></canvas>
                    </div>
                    <div class="kpi-card">
                        <h4 class="kpi-card-title">Joy Score (Organizational Maturity)</h4>
                        <canvas id="joyScoreGauge"></canvas>
                         <p class="text-sm text-gray-500 mt-2">Mid-level Maturity</p>
                    </div>
                </div>
            </div>
            <div class="footer">Page 3 | Confidential</div>
        </div>
        
        <!-- Company Overview & Strategic Context -->
        <div class="page" id="overview">
             <h2>Company Overview & Strategic Context</h2>
             <h3>Business Model & Portfolio</h3>
             <p>CVS Health is a diversified health solutions company operating three primary segments: (1) Health Care Benefits (Aetna®), (2) Health Services (CVS Caremark®), and (3) Pharmacy & Consumer Wellness (CVS Pharmacy®). This model uniquely spans the payer, provider, and retail pharmacy domains. The strategic intent is to create a seamless, “omnichannel” healthcare experience.</p>
             
             <div class="grid grid-cols-1 md:grid-cols-3 gap-6 my-8">
                 <div class="bg-gray-50 p-4 rounded-lg text-center">
                     <h4 class="font-bold text-lg cvs-red">~25%</h4>
                     <p class="text-sm">US Prescription Pharmacy Market Share (#1)</p>
                 </div>
                 <div class="bg-gray-50 p-4 rounded-lg text-center">
                     <h4 class="font-bold text-lg cvs-red">Top 3</h4>
                     <p class="text-sm">Position in U.S. Health Insurance Membership</p>
                 </div>
                 <div class="bg-gray-50 p-4 rounded-lg text-center">
                     <h4 class="font-bold text-lg cvs-red">~9,000</h4>
                     <p class="text-sm">Retail & Pharmacy Stores Nationwide</p>
                 </div>
             </div>

             <h3>External Environment & Competitive Landscape</h3>
             <p>CVS operates amid transformative shifts, including an aging population, rising cost pressures, and increasing consumerization of healthcare. While traditional rivals like Walgreens have struggled, health insurer rivals like UnitedHealth Group have excelled. New entrants like Amazon pose a long-term threat, emphasizing the need for CVS to execute its integrated strategy effectively.</p>

             <h3>Recent Developments</h3>
             <p>A late 2024 leadership change and activist investor pressure have signaled a renewed focus on execution discipline. In response, CVS outlined a $2B cost-savings program and is undertaking strategic portfolio reviews to restore investor trust and unlock value.</p>
             <div class="footer">Page 4 | Confidential</div>
        </div>

        <!-- Financial Performance Baseline -->
        <div class="page" id="financials">
            <h2>Financial Performance Baseline</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                    <h3>Revenue Growth & Profitability</h3>
                    <p>CVS has delivered solid top-line growth (<strong>~7.6% 5-year CAGR</strong>), outpacing many peers. However, margins have been volatile, with operating margin falling to <strong>2.3% in 2024</strong> from ~5% in 2020. This decline was primarily driven by a significant profit downturn in the Health Care Benefits segment due to surging medical costs. Management has signaled confidence in a margin rebound for 2025.</p>
                    <canvas id="revenueTrendChart" class="mt-4"></canvas>
                </div>
                <div>
                    <h3>Leverage & Returns</h3>
                    <p>The company carries a high debt load from acquisitions, with a <strong>Net Debt/EBITDA ratio of ~4.5x</strong>. A key challenge is that its Return on Invested Capital (<strong>ROIC ~5%</strong>) currently trails its Weighted Average Cost of Capital (<strong>WACC ~7-8%</strong>), indicating value erosion. Prioritizing deleveraging and improving profitability is crucial.</p>
                    <canvas id="roicVsWaccChart" class="mt-4"></canvas>
                </div>
            </div>
            <div class="footer">Page 5 | Confidential</div>
        </div>

        <!-- Market & Competitive Benchmarking -->
        <div class="page" id="benchmarking">
            <h2>Market & Competitive Benchmarking</h2>
            <h3>Peer Performance Heatmap</h3>
            <p>Compared to a peer set including UnitedHealth (UNH), Walgreens (WBA), and Cigna, CVS exhibits a mixed profile. It leads on scale and revenue growth but significantly lags on profitability, returns, and leverage metrics. The heatmap below illustrates CVS's relative positioning.</p>
            <div class="overflow-x-auto mt-4">
                <table class="heatmap-table">
                    <thead>
                        <tr>
                            <th>Metric</th>
                            <th class="highlight-col">CVS Health</th>
                            <th>Peer Median</th>
                            <th>Top Quartile</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>Revenue CAGR (5-Yr)</td><td class="q1 highlight-col">7.6%</td><td>6.0%</td><td>>10%</td></tr>
                        <tr><td>EBITDA Margin</td><td class="q4 highlight-col">3.7%</td><td>6.0%</td><td>>7.5%</td></tr>
                        <tr><td>Net Margin</td><td class="q4 highlight-col">1.2%</td><td>3.5%</td><td>>5.0%</td></tr>
                        <tr><td>ROIC</td><td class="q3 highlight-col">~5%</td><td>~8%</td><td>>10%</td></tr>
                        <tr><td>Net Debt/EBITDA</td><td class="q4 highlight-col">~4.5x</td><td>~2.5x</td><td>&lt;2.0x</td></tr>
                        <tr><td>Forward P/E Multiple</td><td class="q3 highlight-col">~10x</td><td>~14x</td><td>>18x</td></tr>
                    </tbody>
                </table>
            </div>
            <div class="mt-8">
                <h3>Valuation Comparison (Forward P/E)</h3>
                <p>CVS's valuation discount is stark when compared to key competitors, reflecting market skepticism about its ability to convert scale into consistent profitability. This gap represents the core of the "Execution Alpha" opportunity.</p>
                <canvas id="peRatioChart"></canvas>
            </div>
            <div class="footer">Page 6 | Confidential</div>
        </div>

        <!-- Organizational Maturity -->
        <div class="page" id="maturity">
            <h2>Organizational Maturity & Execution Agility</h2>
             <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div>
                    <h3>Joy Score: Mid-Level Maturity (3.0/5)</h3>
                    <p>CVS Health’s "Joy Score" is assessed to be approximately <strong>3.0 out of 5</strong>. The organization shows strength in Leadership and Strategy clarity. However, it reveals weaknesses in <strong>Talent & Culture</strong> (evidenced by frontline burnout and turnover) and <strong>Process Agility</strong>. Integrating disparate systems and breaking down data silos remain significant challenges that hinder a unified customer view and rapid decision-making.</p>
                     <h3>Execution Agility: Moderate (SVI ~3.0/5)</h3>
                     <p>Strategic Velocity is moderate. While CVS demonstrated rapid mobilization during the pandemic, the integration of major acquisitions has been slow (~18+ months cycle). Decision-making can be hampered by a decentralized structure, slowing enterprise-wide initiatives. This moderate agility means CVS can execute large projects, but often with a multi-year horizon and significant inertia.</p>
                </div>
                <div>
                    <canvas id="joyScoreRadar"></canvas>
                </div>
            </div>
            <div class="footer">Page 7 | Confidential</div>
        </div>
        
        <!-- Strategic Lens Alternatives & Scenario Analysis -->
        <div class="page" id="strategic-lenses">
            <h2>Strategic Lens Alternatives & Scenario Analysis</h2>
            <p>CVS Health's strategic choices can be viewed through five primary lenses. The <strong>Growth & Expansion</strong> lens is recommended, but must be balanced with elements of other lenses. The chart below contrasts the potential 3-year revenue CAGR and near-term margin impact of each approach.</p>
            <div class="grid grid-cols-1 lg:grid-cols-5 gap-4 mt-6 text-center">
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h4 class="font-bold">Growth & Expansion</h4>
                    <p class="text-sm">Prioritizes top-line growth and market share capture through investments and acquisitions.</p>
                </div>
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h4 class="font-bold">Cost Optimization</h4>
                    <p class="text-sm">Focuses on efficiency, margin improvement, and faster deleveraging.</p>
                </div>
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h4 class="font-bold">Innovation</h4>
                    <p class="text-sm">Centers on differentiating through new tech, offerings, and business models.</p>
                </div>
                 <div class="p-4 bg-gray-50 rounded-lg">
                    <h4 class="font-bold">Operational Excellence</h4>
                    <p class="text-sm">Emphasizes quality, reliability, and productivity in core operations.</p>
                </div>
                 <div class="p-4 bg-gray-50 rounded-lg">
                    <h4 class="font-bold">Customer-Centricity</h4>
                    <p class="text-sm">Organizes around maximizing customer satisfaction, loyalty, and lifetime value.</p>
                </div>
            </div>
            <div class="mt-8">
                 <canvas id="strategicLensesChart"></canvas>
            </div>
            <div class="footer">Page 8 | Confidential</div>
        </div>


        <!-- Risk & Opportunity Mapping -->
        <div class="page" id="risk">
            <h2>Execution Risk & Opportunity Mapping</h2>
            <div class="grid grid-cols-1 lg:grid-cols-5 gap-8">
                <div class="lg:col-span-3">
                    <h3>Execution Risk Matrix</h3>
                    <p>Key execution risks are plotted by likelihood and potential financial impact. The most critical threats—<strong>Insurance Performance Volatility</strong> and <strong>Integration Synergy Shortfall</strong>—reside in the high-impact quadrant, demanding immediate and focused mitigation efforts.</p>
                    <canvas id="riskMatrixChart"></canvas>
                </div>
                <div class="lg:col-span-2">
                    <h3>Execution Alpha by Risk Area</h3>
                    <p>Mitigating these top risks directly translates into unlocked enterprise value. Taming insurance volatility and successfully realizing synergies represent the largest components of the ~$40B Execution Alpha opportunity.</p>
                    <canvas id="alphaByRiskChart"></canvas>
                </div>
            </div>
            <div class="footer">Page 9 | Confidential</div>
        </div>

        <!-- Strategic Recommendations -->
        <div class="page" id="recommendations">
            <h2>Strategic Recommendations</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="priority-box">
                    <h4>Priority 1: Restore Insurance Segment Economics</h4>
                    <p>Strengthen underwriting, manage medical costs aggressively, and improve Medicare Advantage quality metrics to bring Aetna's profitability back in line with peers.</p>
                </div>
                <div class="priority-box">
                    <h4>Priority 2: Maximize Integration Synergies</h4>
                    <p>Create concrete programs to steer Aetna members to CVS care venues, integrate pharmacy and primary care data, and track synergy realization through a dedicated PMO.</p>
                </div>
                <div class="priority-box">
                    <h4>Priority 3: Enhance Customer Experience</h4>
                    <p>Unify the digital journey via a single CVS Health app, amplify personalization using analytics, and strengthen the ExtraCare loyalty program to drive retention.</p>
                </div>
                <div class="priority-box">
                    <h4>Priority 4: Drive Operational Excellence</h4>
                    <p>Continue to optimize the store footprint, implement advanced automation in pharmacy and PBM operations, and use savings to fund strategic growth initiatives.</p>
                </div>
                <div class="priority-box">
                    <h4>Priority 5: Optimize Capital Deployment</h4>
                    <p>Aggressively pay down debt to a target of &lt;3x Debt/EBITDA, conduct a strategic portfolio review, and clearly communicate capital allocation priorities to investors.</p>
                </div>
            </div>
            <div class="footer">Page 10 | Confidential</div>
        </div>

         <!-- 100-Day Plan -->
        <div class="page" id="roadmap">
            <h2>100-Day Plan (Directional Roadmap)</h2>
            <p>To build momentum, leadership should focus on a series of quick wins and foundational actions within the first 100 days.</p>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                <div class="roadmap-phase">
                    <h4>Days 0-30: Mobilize & Align</h4>
                    <ul class="list-disc text-sm">
                        <li>Form Insurance SWAT team & Integration PMO.</li>
                        <li>Launch "Connected Care" pilot in 3 key markets.</li>
                        <li>Initiate customer journey mapping project.</li>
                        <li>Identify $300M in immediate cost savings.</li>
                        <li>Communicate capital plan to investors.</li>
                    </ul>
                </div>
                <div class="roadmap-phase">
                    <h4>Days 31-60: Implement & Test</h4>
                    <ul class="list-disc text-sm">
                        <li>Deploy unified customer feedback dashboard.</li>
                        <li>Pilot new pharmacy staffing models.</li>
                        <li>Finalize 2025 Medicare pricing adjustments.</li>
                        <li>Beta test unified CVS Health app internally.</li>
                    </ul>
                </div>
                <div class="roadmap-phase">
                    <h4>Days 61-100: Review & Scale</h4>
                    <ul class="list-disc text-sm">
                        <li>Review pilot results and present early wins to the board.</li>
                        <li>Develop plan to scale successful tactics nationwide.</li>
                        <li>Lock in further cost initiatives (e.g., store closures).</li>
                        <li>Conduct board strategy session on portfolio.</li>
                        <li>Fill key talent gaps for new initiatives.</li>
                    </ul>
                </div>
            </div>
             <div class="footer">Page 11 | Confidential</div>
        </div>
        
        <!-- Appendices -->
        <div class="page" id="appendices">
            <h2>Appendices</h2>
            <h3>A. Glossary of Terms</h3>
            <dl class="glossary mt-4">
                <dt>Joy Score (Organizational Maturity Index)</dt>
                <dd>Composite 1–5 rating of a firm’s maturity across leadership, culture, processes, etc.</dd>
                <dt>Execution Alpha Index</dt>
                <dd>A measure (often in $) of value that could be gained by closing execution effectiveness gaps.</dd>
                <dt>Investability Score</dt>
                <dd>A 0–100 index indicating the attractiveness of a company to investors, blending financial and execution metrics.</dd>
                <dt>MBR (Medical Benefit Ratio)</dt>
                <dd>The percentage of insurance premium revenue spent on medical claims.</dd>
                <dt>PBM (Pharmacy Benefit Manager)</dt>
                <dd>An intermediary that manages prescription drug benefits on behalf of health insurers.</dd>
            </dl>
             <h3 class="mt-8">B. Execution Alpha Diagram (Detailed)</h3>
             <p>This waterfall chart illustrates how the ~$40B in Execution Alpha is composed of improvements in margin, growth expectations, and risk reduction (which leads to a higher valuation multiple).</p>
             <canvas id="executionAlphaWaterfall"></canvas>
            <div class="footer">Page 12 | Confidential</div>
        </div>


    </div>

    <script>
        // Data and Configuration for Charts
        const cvsRed = '#e11d48';
        const darkBlue = '#1a2a4d';
        const gray = '#6b7280';
        const green = '#16a34a';
        const yellow = '#facc15';
        const red = '#dc2626';

        // 1. Investability Gauge
        const investabilityGaugeCtx = document.getElementById('investabilityGauge').getContext('2d');
        new Chart(investabilityGaugeCtx, {
            type: 'doughnut',
            data: {
                labels: ['Score', 'Remaining'],
                datasets: [{
                    data: [65, 35],
                    backgroundColor: [darkBlue, '#e5e7eb'],
                    borderColor: '#fff',
                    borderWidth: 2
                }]
            },
            options: {
                rotation: -90,
                circumference: 180,
                cutout: '70%',
                plugins: {
                    legend: { display: false },
                    tooltip: { enabled: false }
                }
            }
        });

        // 2. Joy Score Gauge
        const joyScoreGaugeCtx = document.getElementById('joyScoreGauge').getContext('2d');
        new Chart(joyScoreGaugeCtx, {
            type: 'doughnut',
            data: {
                labels: ['Score', 'Remaining'],
                datasets: [{
                    data: [3, 2],
                    backgroundColor: [darkBlue, '#e5e7eb'],
                     borderColor: '#fff',
                    borderWidth: 2
                }]
            },
            options: {
                rotation: -90,
                circumference: 180,
                cutout: '70%',
                 plugins: {
                    legend: { display: false },
                    tooltip: { enabled: false }
                }
            }
        });
        
        // 3. Execution Alpha Chart
        const executionAlphaCtx = document.getElementById('executionAlphaChart').getContext('2d');
        new Chart(executionAlphaCtx, {
            type: 'bar',
            data: {
                labels: ['Current EV', 'Potential EV'],
                datasets: [{
                    label: 'Enterprise Value ($B)',
                    data: [130, 170],
                    backgroundColor: [gray, green],
                    borderColor: [gray, green],
                    borderWidth: 1
                }]
            },
            options: {
                 indexAxis: 'y',
                 scales: {
                     x: {
                         beginAtZero: true,
                         ticks: { callback: value => `$${value}B` }
                     }
                 },
                 plugins: {
                     legend: { display: false }
                 }
            }
        });

        // 4. Revenue Trend Chart
        const revenueTrendCtx = document.getElementById('revenueTrendChart').getContext('2d');
        new Chart(revenueTrendCtx, {
            type: 'line',
            data: {
                labels: ['2020', '2021', '2022', '2023', '2024'],
                datasets: [{
                    label: 'CVS Revenue ($B)',
                    data: [269, 292, 322, 358, 373],
                    borderColor: cvsRed,
                    backgroundColor: 'rgba(225, 29, 72, 0.1)',
                    fill: true,
                    tension: 0.3
                }]
            },
            options: {
                plugins: { legend: { display: false }},
                scales: { y: { ticks: { callback: value => `$${value}B` }}}
            }
        });
        
        // 5. ROIC vs WACC Chart
        const roicVsWaccCtx = document.getElementById('roicVsWaccChart').getContext('2d');
        new Chart(roicVsWaccCtx, {
            type: 'bar',
            data: {
                labels: ['ROIC vs WACC'],
                datasets: [
                    {
                        label: 'ROIC',
                        data: [5],
                        backgroundColor: red
                    },
                    {
                        label: 'WACC',
                        data: [7.5],
                        backgroundColor: gray
                    }
                ]
            },
            options: {
                scales: { y: { beginAtZero: true, ticks: { callback: value => `${value}%` } } },
                plugins: { legend: { position: 'bottom' } }
            }
        });

        // 6. PE Ratio Chart
        const peRatioCtx = document.getElementById('peRatioChart').getContext('2d');
        new Chart(peRatioCtx, {
            type: 'bar',
            data: {
                labels: ['CVS Health', 'Walgreens', 'UnitedHealth', 'Cigna'],
                datasets: [{
                    label: 'Forward P/E Multiple',
                    data: [10, 7, 19, 12],
                    backgroundColor: [cvsRed, gray, darkBlue, darkBlue]
                }]
            },
            options: {
                plugins: { legend: { display: false }},
                scales: { y: { beginAtZero: true }}
            }
        });
        
        // 7. Joy Score Radar Chart
        const joyScoreRadarCtx = document.getElementById('joyScoreRadar').getContext('2d');
        new Chart(joyScoreRadarCtx, {
            type: 'radar',
            data: {
                labels: ['Leadership', 'Strategy', 'People', 'Process', 'Technology', 'Metrics'],
                datasets: [
                    {
                        label: 'CVS Health (Score: 3.0)',
                        data: [3.8, 3.7, 2.5, 2.8, 3.2, 3.0],
                        backgroundColor: 'rgba(225, 29, 72, 0.2)',
                        borderColor: cvsRed,
                        pointBackgroundColor: cvsRed
                    },
                    {
                        label: 'High Performer (Score: 4.5)',
                        data: [4.5, 4.5, 4.2, 4.6, 4.3, 4.4],
                        backgroundColor: 'rgba(26, 42, 77, 0.2)',
                        borderColor: darkBlue,
                        pointBackgroundColor: darkBlue
                    }
                ]
            },
            options: {
                scales: {
                    r: {
                        angleLines: { display: true },
                        suggestedMin: 0,
                        suggestedMax: 5,
                        pointLabels: { font: { size: 12 } }
                    }
                },
                 plugins: { legend: { position: 'bottom' } }
            }
        });

        // 8. Risk Matrix
        const riskMatrixCtx = document.getElementById('riskMatrixChart').getContext('2d');
        new Chart(riskMatrixCtx, {
            type: 'bubble',
            data: {
                datasets: [
                    {
                        label: 'Insurance Volatility',
                        data: [{ x: 3, y: 4.5, r: 25 }],
                        backgroundColor: 'rgba(220, 38, 38, 0.7)'
                    },
                    {
                        label: 'Integration Shortfall',
                        data: [{ x: 3.5, y: 4, r: 20 }],
                        backgroundColor: 'rgba(220, 38, 38, 0.7)'
                    },
                    {
                        label: 'Retail Disruption',
                        data: [{ x: 4, y: 3, r: 15 }],
                         backgroundColor: 'rgba(239, 68, 68, 0.7)'
                    },
                     {
                        label: 'Regulatory Risk',
                        data: [{ x: 2.5, y: 4.2, r: 18 }],
                        backgroundColor: 'rgba(220, 38, 38, 0.7)'
                    },
                    {
                        label: 'Cybersecurity',
                        data: [{ x: 2.5, y: 3.5, r: 12 }],
                        backgroundColor: 'rgba(239, 68, 68, 0.7)'
                    },
                    {
                        label: 'Synergy Upside (Opp.)',
                        data: [{ x: 3, y: 3.8, r: 15 }],
                        backgroundColor: 'rgba(22, 163, 74, 0.7)'
                    },
                ]
            },
            options: {
                scales: {
                    x: {
                        min: 0, max: 5,
                        title: { display: true, text: 'Likelihood (1-5)' }
                    },
                    y: {
                        min: 0, max: 5,
                        title: { display: true, text: 'Impact (1-5)' }
                    }
                },
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label;
                            }
                        }
                    }
                }
            }
        });
        
        // 9. Alpha by Risk Chart
        const alphaByRiskCtx = document.getElementById('alphaByRiskChart').getContext('2d');
        new Chart(alphaByRiskCtx, {
            type: 'bar',
            data: {
                labels: ['Insurance Fix', 'Integration', 'Cost Efficiency', 'Other'],
                datasets: [{
                    label: 'Value at Stake ($B)',
                    data: [20, 10, 5, 5],
                    backgroundColor: [red, red, yellow, gray]
                }]
            },
            options: {
                indexAxis: 'y',
                plugins: { legend: { display: false }},
                scales: { x: { ticks: { callback: value => `$${value}B` }}}
            }
        });

        // 10. Strategic Lenses Chart
        const strategicLensesCtx = document.getElementById('strategicLensesChart').getContext('2d');
        new Chart(strategicLensesCtx, {
            type: 'bar',
            data: {
                labels: ['Growth', 'Cost Opt.', 'Innovation', 'OpEx', 'Customer-Centric'],
                datasets: [
                    {
                        label: 'Projected 3-Yr Revenue CAGR (%)',
                        data: [8, 4, 6, 5, 6.5],
                        backgroundColor: green,
                    },
                    {
                        label: 'Near-Term Margin Impact (%)',
                        data: [-0.5, 1, -1, 0.5, 0], // Representing dip, gain, investment, slight gain, neutral
                        backgroundColor: yellow,
                    }
                ]
            },
            options: {
                plugins: { legend: { position: 'bottom' } },
                scales: {
                    y: { 
                        beginAtZero: true,
                        ticks: { callback: value => `${value}%` }
                    }
                },
                responsive: true,
                maintainAspectRatio: false
            }
        });
        
        // 11. Execution Alpha Waterfall
        const executionAlphaWaterfallCtx = document.getElementById('executionAlphaWaterfall').getContext('2d');
        new Chart(executionAlphaWaterfallCtx, {
            type: 'bar',
            data: {
                labels: ['Current EV', 'Margin Fix', 'Growth', 'Risk Reduction', 'Potential EV'],
                datasets: [{
                    label: 'EV ($B)',
                    data: [
                        [0, 130], // Start
                        [130, 150], // Margin
                        [150, 160], // Growth
                        [160, 170], // Risk
                        [0, 170] // End
                    ],
                    backgroundColor: [gray, green, green, green, darkBlue]
                }]
            },
            options: {
                plugins: { legend: { display: false } },
                 scales: {
                     y: {
                         beginAtZero: true,
                         ticks: { callback: value => `$${value}B` }
                     }
                 },
                 responsive: true,
            }
        });

    </script>
</body>
</html>
