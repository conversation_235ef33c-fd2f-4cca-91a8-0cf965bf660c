import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { loginSchema } from "@shared/schema";
import bcrypt from "bcrypt";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { anthropic } from "@ai-sdk/anthropic";
import { streamText } from "ai";
import { JoyceContextService } from "./joyce-context";
/** PDF generation disabled: client now opens HTML directly */
// import { ReportGenerator } from "./pdf-generator";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export async function registerRoutes(app: Express): Promise<Server> {
  // Serve company data files (handles nested paths like era-report/file.json)
  app.get("/data/companies/:symbol/*", (req, res) => {
    const { symbol } = req.params;
    const filePath = (req.params as any)[0] as string; // Gets everything after /data/companies/:symbol/
    const fullPath = path.join(__dirname, `../client/public/data/companies/${symbol}/${filePath}`);
    
    if (fs.existsSync(fullPath)) {
      res.sendFile(path.resolve(fullPath));
    } else {
      res.status(404).json({ error: "Data file not found" });
    }
  });

  // Joyce Chat API route
  app.post("/api/chat", async (req, res) => {
    try {
      const { messages, companySymbol } = req.body;
      
      // Check if Anthropic API key is configured
      if (!process.env.ANTHROPIC_API_KEY) {
        // Return mock response for demo purposes
        const mockResponse = `Thank you for your question about ${companySymbol}. I'm Joyce, your Strategic Intelligence Assistant. 

Based on the data I can see, I'd be happy to help you analyze your business metrics and provide strategic insights. However, I'm currently running in demo mode without full AI capabilities.

To get real-time AI responses, please configure the ANTHROPIC_API_KEY environment variable with a valid API key.

Is there anything specific about your ${companySymbol} data you'd like me to help explain?`;
        
        res.setHeader('Content-Type', 'text/plain');
        res.write(mockResponse);
        res.end();
        return;
      }
      
      // Build context based on company selection and prompts
      console.log('Building context for company:', companySymbol);
      const context = await JoyceContextService.buildContext(companySymbol);
      console.log('Context built successfully');
      
      console.log('Calling Anthropic API...');
      const result = await streamText({
        model: anthropic("claude-3-5-sonnet-20241022"),
        messages,
        system: context.systemPrompt,
      });
      console.log('Anthropic API call successful');

      // Convert to the data stream response that AI SDK expects
      const dataStreamResponse = result.toDataStreamResponse();
      
      // Copy headers and stream the response
      dataStreamResponse.headers.forEach((value, key) => {
        res.setHeader(key, value);
      });
      
      if (dataStreamResponse.body) {
        const reader = dataStreamResponse.body.getReader();
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            res.write(value);
          }
        } finally {
          reader.releaseLock();
        }
      }
      
      res.end();
    } catch (error) {
      console.error('Chat API error:', error);
      res.status(500).json({ error: 'Failed to process chat request' });
    }
  });

  // Authentication routes
  app.post("/api/auth/login", async (req, res) => {
    try {
      const { username, password } = loginSchema.parse(req.body);
      
      const user = await storage.getUserByUsername(username);
      if (!user) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Password check: support bcrypt-hashed (from Postgres) and plaintext (MemStorage dev seed)
      let valid = false;
      try {
        if (typeof user.password === "string" && user.password.startsWith("$2")) {
          valid = await bcrypt.compare(password, user.password);
        } else {
          valid = password === user.password;
        }
      } catch {
        valid = false;
      }
      if (!valid) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      res.json({ 
        user: { 
          id: user.id, 
          username: user.username, 
          email: user.email, 
          role: user.role 
        } 
      });
    } catch (error) {
      res.status(400).json({ message: "Invalid request data" });
    }
  });

  app.post("/api/auth/logout", (req, res) => {
    res.json({ message: "Logged out successfully" });
  });

  // Company directory listing route
  app.get("/api/companies/list", async (req, res) => {
    try {
      const companiesDir = path.join(__dirname, '../client/public/data/companies');
      
      // Check if the companies directory exists
      if (!fs.existsSync(companiesDir)) {
        return res.json([]);
      }
      
      const directories = fs.readdirSync(companiesDir)
        .filter(item => {
          const fullPath = path.join(companiesDir, item);
          return fs.statSync(fullPath).isDirectory();
        })
        .filter(dir => {
          // Only include directories that have an executive-summary.json file
          const summaryPath = path.join(companiesDir, dir, 'executive-summary.json');
          return fs.existsSync(summaryPath);
        });
      
      res.json(directories);
    } catch (error) {
      console.error('Error listing companies:', error);
      res.status(500).json({ error: 'Cannot read companies directory' });
    }
  });

  // Company routes
  app.get("/api/companies", async (req, res) => {
    try {
      const companies = await storage.getCompanies();
      res.json(companies);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch companies" });
    }
  });

  app.get("/api/companies/:symbol", async (req, res) => {
    try {
      const { symbol } = req.params;
      const company = await storage.getCompany(symbol);
      
      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }
      
      res.json(company);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch company" });
    }
  });

  // KPI metrics routes
  app.get("/api/companies/:symbol/kpis", async (req, res) => {
    try {
      const { symbol } = req.params;
      const company = await storage.getCompany(symbol);
      
      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }
      
      const kpis = await storage.getKpiMetrics(company.id);
      res.json(kpis);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch KPI metrics" });
    }
  });

  // Performance data routes
  app.get("/api/companies/:symbol/performance", async (req, res) => {
    try {
      const { symbol } = req.params;
      const company = await storage.getCompany(symbol);
      
      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }
      
      const performance = await storage.getPerformanceData(company.id);
      res.json(performance);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch performance data" });
    }
  });

  // Strategic insights routes
  app.get("/api/companies/:symbol/insights", async (req, res) => {
    try {
      const { symbol } = req.params;
      const company = await storage.getCompany(symbol);
      
      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }
      
      const insights = await storage.getStrategicInsights(company.id);
      res.json(insights);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch strategic insights" });
    }
  });

  // Execution priorities routes
  app.get("/api/companies/:symbol/priorities", async (req, res) => {
    try {
      const { symbol } = req.params;
      const company = await storage.getCompany(symbol);
      
      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }
      
      const priorities = await storage.getExecutionPriorities(company.id);
      res.json(priorities);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch execution priorities" });
    }
  });

  // Test endpoint to check data availability
  app.get("/api/reports/data/:reportType/:symbol", async (req, res) => {
    try {
      const { reportType, symbol } = req.params;
      
      const companiesPath = path.join(__dirname, '../client/public/data/companies', symbol.toLowerCase());
      console.log(`Checking data path: ${companiesPath}`);
      
      if (!fs.existsSync(companiesPath)) {
        return res.status(404).json({
          message: `Company data not found for ${symbol}`,
          path: companiesPath
        });
      }
      
      let sections: string[] = [];
      if (reportType === 'sei') {
        sections = ['executive-summary', 'enterprise-layer', 'products-services', 'customer-engagement', 'organizational-maturity', 'execution-roadmap'];
      } else if (reportType === 'era') {
        sections = ['executive-summary-dashboard', 'company-context-benchmark-explorer', 'execution-maturity-joy-score-deep-dive', 'agility-simulator', 'value-leakage-financial-impact', 'strategic-lens-engine', 'best-practice-library', 'recommendations-roadmap'];
      }
      
      const availableSections = sections.filter(section => {
        const sectionPath = reportType === 'era' 
          ? path.join(companiesPath, 'era-report', `${section}.json`)
          : path.join(companiesPath, `${section}.json`);
        return fs.existsSync(sectionPath);
      });
      
      res.json({
        company: symbol,
        reportType,
        totalSections: sections.length,
        availableSections: availableSections.length,
        sections: availableSections
      });
      
    } catch (error) {
      console.error('Data check error:', error);
      res.status(500).json({ message: "Failed to check data availability" });
    }
  });

  // PDF generation route (disabled). Use client-side HTML open instead.
  // app.get("/api/reports/pdf/:reportType/:symbol", async (req, res) => {
  //   res.status(410).json({ message: "PDF generation is disabled. Open the HTML report instead." });
  // });

  // Serve static files from server/files
  app.get("/files/*", (req, res) => {
    try {
      const fileSubpath = (req.params as any)[0] as string;
      const baseDir = path.join(__dirname, "../server/files");
      const resolvedPath = path.resolve(baseDir, fileSubpath);

      // Path traversal protection
      if (!resolvedPath.startsWith(baseDir)) {
        return res.status(400).json({ error: "Invalid path" });
      }

      if (fs.existsSync(resolvedPath)) {
        return res.sendFile(resolvedPath);
      }

      return res.status(404).json({ error: "File not found" });
    } catch (err) {
      console.error("File serve error:", err);
      return res.status(500).json({ error: "Failed to serve file" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
