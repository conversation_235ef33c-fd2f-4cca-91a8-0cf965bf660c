import { PDFGenerator } from "./pdf-generator";

async function testPDF() {
  try {
    console.log('Testing PDF generation...');
    
    const pdfBuffer = await PDFGenerator.generatePDF({
      reportType: 'sei',
      symbol: 'vz',
      format: 'A4',
      landscape: false
    });
    
    console.log(`PDF generated successfully! Size: ${pdfBuffer.length} bytes`);
    
    // Save to file for testing
    const fs = await import('fs');
    fs.writeFileSync('/tmp/test-report.pdf', pdfBuffer);
    console.log('PDF saved to /tmp/test-report.pdf');
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testPDF();