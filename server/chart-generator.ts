import { ChartConfiguration, Chart, registerables } from 'chart.js';
import { Canvas, createCanvas } from 'canvas';

// Register Chart.js components for server-side rendering
Chart.register(...registerables);

// Custom plugin for center text in gauge charts
const centerTextPlugin = {
  id: 'centerText',
  beforeDraw: (chart: any) => {
    if (chart.config.type !== 'doughnut') return;
    
    const ctx = chart.ctx;
    const centerX = chart.chartArea.left + (chart.chartArea.right - chart.chartArea.left) / 2;
    const centerY = chart.chartArea.top + (chart.chartArea.bottom - chart.chartArea.top) / 2 + 20;
    
    // Get the data for center text
    const dataset = chart.data.datasets[0];
    const total = dataset.data.reduce((a: number, b: number) => a + b, 0);
    const value = dataset.data[0];
    
    ctx.save();
    ctx.font = 'bold 28px Arial';
    ctx.fillStyle = '#1e293b';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(`${value}`, centerX, centerY - 10);
    
    ctx.font = '14px Arial';
    ctx.fillStyle = '#64748b';
    ctx.fillText(`of ${total}`, centerX, centerY + 15);
    ctx.restore();
  }
};

Chart.register(centerTextPlugin);

export interface ChartOptions {
  width?: number;
  height?: number;
  type: 'gauge' | 'radar' | 'waterfall' | 'heatmap' | 'bar' | 'line' | 'bubble' | 'risk-matrix';
  data: any;
  title?: string;
  colors?: string[];
  subtitle?: string;
}

export class ChartGenerator {
  private static defaultColors = [
    '#667eea', '#764ba2', '#f093fb', '#f5576c', 
    '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
  ];

  public static async generateChart(options: ChartOptions): Promise<Buffer> {
    const { width = 800, height = 600, type, data, title, colors = this.defaultColors } = options;
    
    const canvas = createCanvas(width, height);
    const ctx = canvas.getContext('2d');
    
    let config: ChartConfiguration;
    
    switch (type) {
      case 'gauge':
        config = this.createGaugeConfig(data, title, colors);
        break;
      case 'radar':
        config = this.createRadarConfig(data, title, colors);
        break;
      case 'waterfall':
        config = this.createWaterfallConfig(data, title, colors);
        break;
      case 'heatmap':
        config = this.createHeatmapConfig(data, title, colors);
        break;
      case 'bar':
        config = this.createBarConfig(data, title, colors);
        break;
      case 'line':
        config = this.createLineConfig(data, title, colors);
        break;
      case 'bubble':
        config = this.createBubbleConfig(data, title, colors);
        break;
      default:
        throw new Error(`Unsupported chart type: ${type}`);
    }
    
    // Create chart instance
    const chart = new Chart(ctx as any, config);
    
    // Wait for chart to render
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Get image buffer
    const buffer = canvas.toBuffer('image/png');
    
    // Clean up
    chart.destroy();
    
    return buffer;
  }

  private static createGaugeConfig(data: any, title?: string, colors?: string[]): ChartConfiguration {
    // Implement gauge as a doughnut chart with ERA-style formatting
    const { value, max = 100, label, color = colors?.[0] } = data;
    
    // Calculate percentages for gauge styling
    const percentage = (value / max) * 100;
    const remainingPercentage = 100 - percentage;
    
    // Color coding based on performance (ERA best practice)
    let gaugeColor = color;
    if (percentage >= 80) gaugeColor = '#059669'; // Green for high performance
    else if (percentage >= 60) gaugeColor = '#d97706'; // Orange for medium
    else gaugeColor = '#dc2626'; // Red for low performance
    
    return {
      type: 'doughnut',
      data: {
        datasets: [{
          data: [value, max - value],
          backgroundColor: [gaugeColor, '#f1f5f9'],
          borderWidth: 2,
          borderColor: [gaugeColor, '#e2e8f0'],
          circumference: 180,
          rotation: 270
        }]
      },
      options: {
        responsive: false,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          title: {
            display: !!title,
            text: title,
            font: { size: 16, weight: 'bold' },
            color: '#1e293b'
          },
          // Custom center text plugin
          beforeDraw: (chart: any) => {
            const ctx = chart.ctx;
            const centerX = chart.chartArea.left + (chart.chartArea.right - chart.chartArea.left) / 2;
            const centerY = chart.chartArea.top + (chart.chartArea.bottom - chart.chartArea.top) / 2 + 20;
            
            ctx.save();
            ctx.font = 'bold 24px Arial';
            ctx.fillStyle = '#1e293b';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(`${value}/${max}`, centerX, centerY);
            
            ctx.font = '12px Arial';
            ctx.fillStyle = '#64748b';
            ctx.fillText(label || 'Score', centerX, centerY + 25);
            ctx.restore();
          }
        },
        cutout: '75%'
      }
    };
  }

  private static createRadarConfig(data: any, title?: string, colors?: string[]): ChartConfiguration {
    const { dimensions, companyScores, benchmarkScores, labels } = data;
    
    return {
      type: 'radar',
      data: {
        labels: labels || dimensions,
        datasets: [
          {
            label: 'Company Score',
            data: companyScores,
            backgroundColor: (colors?.[0] || '#3b82f6') + '30',
            borderColor: colors?.[0] || '#3b82f6',
            borderWidth: 3,
            fill: true,
            pointBackgroundColor: colors?.[0] || '#3b82f6',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 6
          },
          {
            label: 'Industry Benchmark',
            data: benchmarkScores,
            backgroundColor: 'transparent',
            borderColor: '#64748b',
            borderWidth: 2,
            borderDash: [8, 4],
            fill: false,
            pointBackgroundColor: '#64748b',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 4
          }
        ]
      },
      options: {
        responsive: false,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: !!title,
            text: title,
            font: { size: 18, weight: 'bold' },
            color: '#1e293b',
            padding: { bottom: 20 }
          },
          legend: {
            position: 'bottom',
            labels: {
              font: { size: 12 },
              padding: 20,
              usePointStyle: true
            }
          }
        },
        scales: {
          r: {
            min: 0,
            max: 5,
            ticks: {
              stepSize: 1,
              font: { size: 11 },
              color: '#64748b'
            },
            grid: {
              color: '#e2e8f0'
            },
            angleLines: {
              color: '#e2e8f0'
            },
            pointLabels: {
              font: { size: 12, weight: 'bold' },
              color: '#374151'
            }
          }
        }
      }
    };
  }

  private static createWaterfallConfig(data: any, title?: string, colors?: string[]): ChartConfiguration {
    // Enhanced waterfall chart for value analysis
    const { categories, values, startValue = 0 } = data;
    
    // Calculate cumulative values for waterfall effect
    let runningTotal = startValue;
    const waterfallData = categories.map((category: string, index: number) => {
      if (index === 0) {
        // Starting value
        return { x: category, y: startValue, isStart: true };
      } else if (index === categories.length - 1) {
        // Ending value
        return { x: category, y: runningTotal, isEnd: true };
      } else {
        // Intermediate values
        const value = values[index - 1];
        const previousTotal = runningTotal;
        runningTotal += value;
        return { 
          x: category, 
          y: Math.abs(value), 
          base: value >= 0 ? previousTotal : runningTotal,
          value: value,
          isPositive: value >= 0
        };
      }
    });

    return {
      type: 'bar',
      data: {
        labels: categories,
        datasets: [{
          data: waterfallData.map(d => d.y),
          backgroundColor: waterfallData.map(d => {
            if (d.isStart) return '#64748b';
            if (d.isEnd) return '#059669';
            return d.isPositive ? '#10b981' : '#dc2626';
          }),
          borderColor: '#ffffff',
          borderWidth: 2
        }]
      },
      options: {
        responsive: false,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: !!title,
            text: title,
            font: { size: 18, weight: 'bold' },
            color: '#1e293b',
            padding: { bottom: 20 }
          },
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: function(context: any) {
                const dataPoint = waterfallData[context.dataIndex];
                if (dataPoint.isStart) return `Starting Value: $${dataPoint.y.toFixed(1)}B`;
                if (dataPoint.isEnd) return `Final Value: $${dataPoint.y.toFixed(1)}B`;
                return `${dataPoint.isPositive ? '+' : ''}$${dataPoint.value.toFixed(1)}B`;
              }
            }
          }
        },
        scales: {
          x: {
            title: {
              display: true,
              text: 'Value Components',
              font: { size: 14, weight: 'bold' }
            },
            ticks: {
              font: { size: 11 }
            }
          },
          y: {
            title: {
              display: true,
              text: 'Enterprise Value ($B)',
              font: { size: 14, weight: 'bold' }
            },
            ticks: {
              font: { size: 11 },
              callback: function(value: any) {
                return '$' + value + 'B';
              }
            },
            grid: {
              color: '#f1f5f9'
            }
          }
        }
      }
    };
  }

  private static createHeatmapConfig(data: any, title?: string, colors?: string[]): ChartConfiguration {
    // Enhanced heatmap for ERA benchmarking with quartile color coding
    const { matrix, rowLabels, columnLabels } = data;
    
    // Quartile colors (ERA best practice)
    const quartileColors = ['#dc2626', '#f59e0b', '#10b981', '#059669']; // Red, Orange, Light Green, Dark Green
    
    const flatData = matrix.flat().map((value: number, index: number) => {
      const x = index % columnLabels.length;
      const y = Math.floor(index / columnLabels.length);
      
      // Color based on quartile (1=bottom, 4=top)
      const colorIndex = Math.max(0, Math.min(3, Math.floor(value) - 1));
      
      return {
        x,
        y,
        v: value,
        backgroundColor: quartileColors[colorIndex],
        label: `${rowLabels[y]}: ${columnLabels[x]} (Q${value})`
      };
    });
    
    return {
      type: 'scatter',
      data: {
        datasets: [{
          data: flatData,
          backgroundColor: flatData.map(d => d.backgroundColor),
          pointRadius: 25,
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      },
      options: {
        responsive: false,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: !!title,
            text: title,
            font: { size: 18, weight: 'bold' },
            color: '#1e293b',
            padding: { bottom: 20 }
          },
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: function(context: any) {
                const point = context.raw;
                return point.label;
              }
            }
          }
        },
        scales: {
          x: {
            type: 'linear',
            position: 'bottom',
            min: -0.5,
            max: columnLabels.length - 0.5,
            title: {
              display: true,
              text: 'Companies',
              font: { size: 14, weight: 'bold' }
            },
            ticks: {
              stepSize: 1,
              callback: function(value: number) {
                return columnLabels[value] || '';
              },
              font: { size: 11 }
            },
            grid: {
              display: false
            }
          },
          y: {
            type: 'linear',
            min: -0.5,
            max: rowLabels.length - 0.5,
            title: {
              display: true,
              text: 'Key Performance Indicators',
              font: { size: 14, weight: 'bold' }
            },
            ticks: {
              stepSize: 1,
              callback: function(value: number) {
                return rowLabels[value] || '';
              },
              font: { size: 11 }
            },
            grid: {
              display: false
            }
          }
        }
      }
    };
  }

  private static createBarConfig(data: any, title?: string, colors?: string[]): ChartConfiguration {
    const { labels, datasets } = data;
    
    return {
      type: 'bar',
      data: {
        labels,
        datasets: datasets.map((dataset: any, index: number) => ({
          ...dataset,
          backgroundColor: dataset.backgroundColor || colors?.[index % colors.length],
          borderColor: dataset.borderColor || colors?.[index % colors.length],
          borderWidth: 1
        }))
      },
      options: {
        responsive: false,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: !!title,
            text: title,
            font: { size: 16, weight: 'bold' }
          }
        },
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    };
  }

  private static createLineConfig(data: any, title?: string, colors?: string[]): ChartConfiguration {
    const { labels, datasets } = data;
    
    return {
      type: 'line',
      data: {
        labels,
        datasets: datasets.map((dataset: any, index: number) => ({
          ...dataset,
          borderColor: dataset.borderColor || colors?.[index % colors.length],
          backgroundColor: dataset.backgroundColor || colors?.[index % colors.length] + '20',
          borderWidth: 2,
          fill: false
        }))
      },
      options: {
        responsive: false,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: !!title,
            text: title,
            font: { size: 16, weight: 'bold' }
          }
        },
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    };
  }

  private static createBubbleConfig(data: any, title?: string, colors?: string[]): ChartConfiguration {
    const { datasets, xAxisLabel, yAxisLabel } = data;
    
    return {
      type: 'scatter',
      data: {
        datasets: datasets.map((dataset: any, index: number) => ({
          ...dataset,
          backgroundColor: dataset.backgroundColor || (colors?.[index % colors.length] || '#3b82f6') + '80',
          borderColor: dataset.borderColor || colors?.[index % colors.length] || '#3b82f6',
          borderWidth: 2,
          pointRadius: dataset.data.map((point: any) => point.r || 8),
          // Add labels for each point
          data: dataset.data.map((point: any) => ({
            x: point.x,
            y: point.y,
            label: point.label
          }))
        }))
      },
      options: {
        responsive: false,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: !!title,
            text: title,
            font: { size: 18, weight: 'bold' },
            color: '#1e293b',
            padding: { bottom: 20 }
          },
          legend: {
            position: 'bottom',
            labels: {
              font: { size: 12 }
            }
          },
          tooltip: {
            callbacks: {
              label: function(context: any) {
                const point = context.raw;
                return `${point.label}: (${point.x}, ${point.y})`;
              }
            }
          }
        },
        scales: {
          x: {
            type: 'linear',
            position: 'bottom',
            min: 0,
            max: 5,
            title: {
              display: true,
              text: xAxisLabel || 'X Axis',
              font: { size: 14, weight: 'bold' },
              color: '#374151'
            },
            ticks: {
              stepSize: 1,
              font: { size: 11 }
            },
            grid: {
              color: '#f1f5f9'
            }
          },
          y: {
            type: 'linear',
            min: 0,
            max: 5,
            title: {
              display: true,
              text: yAxisLabel || 'Y Axis',
              font: { size: 14, weight: 'bold' },
              color: '#374151'
            },
            ticks: {
              stepSize: 1,
              font: { size: 11 }
            },
            grid: {
              color: '#f1f5f9'
            }
          }
        }
      }
    };
  }

  // Utility method to generate chart and return base64 data URL
  public static async generateChartDataURL(options: ChartOptions): Promise<string> {
    const buffer = await this.generateChart(options);
    return `data:image/png;base64,${buffer.toString('base64')}`;
  }
  
  // ERA-specific chart generators
  public static async generateInvestabilityGauge(score: number): Promise<string> {
    return this.generateChartDataURL({
      type: 'gauge',
      data: {
        value: score,
        max: 100,
        label: 'Investability Score',
        color: '#3b82f6'
      },
      title: 'Investability Score',
      width: 300,
      height: 250
    });
  }
  
  public static async generateExecutionAlphaChart(currentEV: number, potentialEV: number, breakdown?: any[]): Promise<string> {
    const gap = potentialEV - currentEV;
    
    if (breakdown) {
      // Generate waterfall chart showing breakdown
      const categories = ['Current EV', ...breakdown.map(b => b.category), 'Potential EV'];
      const values = [currentEV, ...breakdown.map(b => b.value), 0]; // Final value calculated automatically
      
      return this.generateChartDataURL({
        type: 'waterfall',
        data: { categories, values, startValue: currentEV },
        title: `Execution Alpha: $${gap.toFixed(1)}B Value Upside`,
        width: 700,
        height: 400
      });
    } else {
      // Simple bar comparison
      return this.generateChartDataURL({
        type: 'bar',
        data: {
          labels: ['Current Enterprise Value', 'Potential Enterprise Value'],
          datasets: [{
            data: [currentEV, potentialEV],
            backgroundColor: ['#64748b', '#059669'],
            borderWidth: 1
          }]
        },
        title: `Execution Alpha: $${gap.toFixed(1)}B Value Gap`,
        width: 500,
        height: 300
      });
    }
  }
  
  public static async generatePeerBenchmarkHeatmap(metrics: any[], peers: string[], companyData: any): Promise<string> {
    // Create matrix data for heatmap visualization
    const matrix = metrics.map(metric => {
      return peers.map(peer => {
        const peerValue = metric.peerData?.[peer] || 0;
        const companyValue = metric.companyValue || 0;
        
        // Normalize to quartile (1-4 scale for color coding)
        if (peer === 'Company') return this.getQuartileRank(companyValue, metric.benchmarks);
        return this.getQuartileRank(peerValue, metric.benchmarks);
      });
    });
    
    return this.generateChartDataURL({
      type: 'heatmap',
      data: {
        matrix,
        rowLabels: metrics.map(m => m.name || m.metric),
        columnLabels: peers
      },
      title: 'Competitive Benchmark Analysis',
      width: 800,
      height: 500
    });
  }
  
  private static getQuartileRank(value: number, benchmarks: any): number {
    if (!benchmarks) return 2; // Default to median
    
    if (value >= benchmarks.topQuartile) return 4;
    if (value >= benchmarks.median) return 3;
    if (value >= benchmarks.bottomQuartile) return 2;
    return 1;
  }
}