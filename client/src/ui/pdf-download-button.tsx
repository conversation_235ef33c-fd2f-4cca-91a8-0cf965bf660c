import { useState } from "react";
import { Button } from "./button";
import { Download, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface PDFDownloadButtonProps {
  reportType: 'sei' | 'era';
  companySymbol: string;
  filePath?: string; // target HTML to open (defaults to PDF report)
  format?: 'A4' | 'Letter';
  landscape?: boolean;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
  className?: string;
  children?: React.ReactNode;
}

export function PDFDownloadButton({
  reportType,
  companySymbol,
  filePath,
  format = 'A4',
  landscape = false,
  variant = 'outline',
  size = 'default',
  className,
  children
}: PDFDownloadButtonProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();

  const handleDownload = async () => {
    setIsGenerating(true);

    try {
      const htmlUrl = filePath ?? `/files/PDF_Report.html`;
      window.open(htmlUrl, '_blank', 'noopener,noreferrer');

      toast({
        title: "Opening HTML Report",
        description: `${reportType.toUpperCase()} report opened in a new tab.`,
      });
    } catch (error) {
      console.error('Open HTML report error:', error);
      toast({
        title: "Failed to open report",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Button
      onClick={handleDownload}
      disabled={isGenerating || reportType === 'sei'}
      title={reportType === 'sei' ? 'Download not available for SEI report' : undefined}
      variant={variant}
      size={size}
      className={className}
    >
      {isGenerating ? (
        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
      ) : (
        <Download className="w-4 h-4 mr-2" />
      )}
      {children || (isGenerating ? 'Generating PDF...' : 'Download PDF')}
    </Button>
  );
}
