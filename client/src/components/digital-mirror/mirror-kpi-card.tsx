import { Card, CardContent } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { But<PERSON> } from "@/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/ui/tooltip";
import { 
  TrendingUp, 
  TrendingDown, 
  Minus, 
  Cloud, 
  AlertTriangle, 
  BarChart3,
  MessageCircle,
  Info
} from "lucide-react";

export interface MirrorKPIData {
  id: string;
  name: string;
  icon?: React.ComponentType<any>;
  currentValue: number;
  unit: string;
  target?: number;
  benchmark?: number;
  trend: number[]; // Array of values for sparkline
  trendData?: { period: string; value: number }[]; // Optional labeled series for detailed charts
  variance: "up" | "down" | "flat";
  benchmarkDelta: number;
  fogScore: number; // 0-100, confidence level
  maturityLevel?: string; // For ERA mode
  executionAlphaImpact?: number; // Backwards-compat: kept for sorting; mirrors impactUSD
  impactUSD?: number; // Dollar impact to reach target
  advantageVsPeerUSD?: number; // Dollar advantage vs peer (non-negative)
  drivers: string[]; // Top 2-3 causal factors
  joyceInsight?: string;
  layer: "enterprise" | "product" | "customer";
  category: "financial" | "customer" | "operational" | "maturity";
  status: "on-track" | "at-risk" | "off-track";
}

interface MirrorKPICardProps {
  kpi: MirrorKPIData;
  mode: "era" | "sei";
  onClick?: (kpi: MirrorKPIData) => void;
  onJoyceAsk?: (kpi: MirrorKPIData) => void;
  isSelected?: boolean;
}

export function MirrorKPICard({ 
  kpi, 
  mode, 
  onClick, 
  onJoyceAsk, 
  isSelected = false 
}: MirrorKPICardProps) {
  const getStatusColor = () => {
    switch (kpi.status) {
      case "on-track": return "border-green-500 bg-green-50";
      case "at-risk": return "border-amber-500 bg-amber-50";
      case "off-track": return "border-red-500 bg-red-50";
      default: return "border-gray-200 bg-white";
    }
  };

  const getStatusDot = () => {
    switch (kpi.status) {
      case "on-track": return "bg-green-500";
      case "at-risk": return "bg-amber-500";
      case "off-track": return "bg-red-500";
      default: return "bg-gray-400";
    }
  };

  const getVarianceIcon = () => {
    switch (kpi.variance) {
      case "up": return <TrendingUp className="w-3 h-3 text-green-600" />;
      case "down": return <TrendingDown className="w-3 h-3 text-red-600" />;
      case "flat": return <Minus className="w-3 h-3 text-gray-600" />;
    }
  };

  const formatNumber = (value: number, options: Intl.NumberFormatOptions = {}) => {
    // Generic number formatter: no more than 2 decimals, no unnecessary trailing zeros
    return value.toLocaleString(undefined, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
      ...options,
    });
  };

  const formatCurrency = (value: number) => {
    const abs = Math.abs(value);
    if (abs >= 1_000_000_000) return `$${formatNumber(value / 1_000_000_000, { maximumFractionDigits: 2 })}B`;
    if (abs >= 1_000_000) return `$${formatNumber(value / 1_000_000, { maximumFractionDigits: 2 })}M`;
    if (abs >= 1_000) return `$${formatNumber(value / 1_000, { maximumFractionDigits: 2 })}K`;
    return `$${formatNumber(value, { maximumFractionDigits: 2 })}`;
  };

  const formatValue = (value: number) => {
    // Large currency values (e.g., $1.2M)
    if (kpi.unit === "$" && value >= 1_000_000) {
      return `$${formatNumber(value / 1_000_000, { maximumFractionDigits: 2 })}M`;
    }
    // Percent/points values (keep a concise decimal, never more than 2)
    if (kpi.unit === "%" || kpi.unit === "pts") {
      return `${formatNumber(value, { maximumFractionDigits: 1 })}${kpi.unit}`;
    }
    // Thousands suffix (e.g., 2.4K)
    if (value >= 1_000) {
      return `${formatNumber(value / 1_000, { maximumFractionDigits: 2 })}K`;
    }
    // Default: cap at 2 decimals
    return formatNumber(value);
  };

  const targetGap = kpi.target ? ((kpi.currentValue - kpi.target) / kpi.target * 100) : 0;
  const benchmarkGap = kpi.benchmark ? ((kpi.currentValue - kpi.benchmark) / kpi.benchmark * 100) : 0;

  return (
    <Card 
      className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
        getStatusColor()
      } ${isSelected ? 'ring-2 ring-blue-500' : ''}`}
      onClick={() => onClick?.(kpi)}
    >
      <CardContent className="p-4">
        {/* Header Row */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-2">
            {kpi.icon && <kpi.icon className="w-4 h-4 text-gray-600" />}
            <div className={`w-2 h-2 rounded-full ${getStatusDot()}`} />
          </div>
          <div className="flex items-center gap-1">
            {kpi.fogScore < 70 && (
              <Tooltip>
                <TooltipTrigger>
                  <Cloud className="w-3 h-3 text-gray-400" />
                </TooltipTrigger>
                <TooltipContent>
                  Fog Score: {kpi.fogScore}% confidence
                </TooltipContent>
              </Tooltip>
            )}
            {kpi.benchmark && (
              <Tooltip>
                <TooltipTrigger>
                  <BarChart3 className="w-3 h-3 text-blue-500" />
                </TooltipTrigger>
                <TooltipContent>
                  Benchmark available
                </TooltipContent>
              </Tooltip>
            )}
            {Math.abs(targetGap) > 10 && (
              <AlertTriangle className="w-3 h-3 text-amber-500" />
            )}
          </div>
        </div>

        {/* KPI Name */}
        <div className="text-sm font-medium text-gray-900 mb-2 leading-tight">
          {kpi.name}
        </div>

        {/* Current Value */}
        <div className="text-2xl font-bold text-gray-900 mb-2">
          {formatValue(kpi.currentValue)}
          <span className="text-sm font-normal text-gray-500 ml-1">{kpi.unit}</span>
        </div>

        {/* Target & Benchmark Row */}
        <div className="flex items-center gap-4 mb-2 text-xs">
          {kpi.target && (
            <div className="flex items-center gap-1">
              <span className="text-gray-500">Target:</span>
              <span className="font-medium">{formatValue(kpi.target)}</span>
              <span className={`font-medium ${targetGap > 0 ? 'text-green-600' : 'text-red-600'}`}>
                ({targetGap > 0 ? '+' : ''}{targetGap.toFixed(1)}%)
              </span>
            </div>
          )}
          {kpi.benchmark && (
            <div className="flex items-center gap-1">
              <span className="text-gray-500">Peer:</span>
              <span className="font-medium">{formatValue(kpi.benchmark)}</span>
            </div>
          )}
        </div>

        {/* Trend & Variance */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            {getVarianceIcon()}
            <span className="text-xs text-gray-600">
              {Math.abs(kpi.benchmarkDelta).toFixed(1)}% vs peers
            </span>
          </div>
          {/* Simple sparkline representation */}
          <div className="flex items-center gap-1">
            {kpi.trend.slice(-6).map((value, index) => (
              <div
                key={index}
                className={`w-1 bg-gray-300 rounded-sm ${
                  value > kpi.currentValue ? 'h-3' : value < kpi.currentValue ? 'h-1' : 'h-2'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Mode-specific content */}
        {mode === "era" && kpi.maturityLevel && (
          <div className="mb-2">
            <Badge variant="outline" className="text-xs">
              {kpi.maturityLevel}
            </Badge>
          </div>
        )}

        {mode === "sei" && (
          <div className="mb-2">
            <div className="text-xs text-blue-600 font-medium">
              {(kpi.impactUSD ?? 0) > 0
                ? `Impact: ${formatCurrency(kpi.impactUSD!)} upside`
                : (kpi.advantageVsPeerUSD ?? 0) > 0
                  ? `Advantage vs peer: ${formatCurrency(kpi.advantageVsPeerUSD!)}`
                  : kpi.target !== undefined
                    ? "Impact: At/above target"
                    : kpi.benchmark !== undefined
                      ? "Impact: On par with peers"
                      : "Impact: $0"}
            </div>
          </div>
        )}

        {/* Drivers */}
        <div className="mb-3">
          <div className="text-xs text-gray-500 mb-1">Top Drivers:</div>
          <div className="space-y-1">
            {kpi.drivers.slice(0, 2).map((driver, index) => (
              <div key={index} className="text-xs text-gray-700 leading-tight">
                • {driver}
              </div>
            ))}
          </div>
        </div>

        {/* Joyce CTA */}
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            size="sm"
            className="text-xs h-7 px-2 text-purple-600 hover:text-purple-700"
            onClick={(e) => {
              e.stopPropagation();
              onJoyceAsk?.(kpi);
            }}
          >
            <MessageCircle className="w-3 h-3 mr-1" />
            Ask Joyce
          </Button>
          
          <Tooltip>
            <TooltipTrigger>
              <Info className="w-3 h-3 text-gray-400" />
            </TooltipTrigger>
            <TooltipContent className="max-w-xs">
              <div className="space-y-1 text-xs">
                <div>Confidence: {kpi.fogScore}%</div>
                <div>Last 3 months trend: {kpi.variance}</div>
                {kpi.benchmark && (
                  <div>Benchmark delta: {kpi.benchmarkDelta.toFixed(1)}%</div>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        </div>
      </CardContent>
    </Card>
  );
}
