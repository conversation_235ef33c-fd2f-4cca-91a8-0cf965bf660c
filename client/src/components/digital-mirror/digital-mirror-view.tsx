import { useState, useMemo, useEffect } from "react";
import { useLocation } from "wouter";
import { MirrorKPICard, MirrorKPIData } from "./mirror-kpi-card";
import { SankeyKPIDiagram } from "./sankey-kpi-diagram";
import { FilterToolbar, FilterState } from "./filter-toolbar";
import { Button } from "@/ui/button";
import { Badge } from "@/ui/badge";
import { ToggleGroup, ToggleGroupItem } from "@/ui/toggle-group";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/ui/collapsible";
import { ChevronDown, Download, Grid, X, GitBranch } from "lucide-react";
import { ResponsiveContainer, LineChart, Line, CartesianGrid, XAxis, YAxis, Tooltip as RechartsTooltip } from "recharts";
interface DigitalMirrorViewProps {
  kpis: MirrorKPIData[];
  onJoyceOpen?: (questions: string[]) => void;
}

export function DigitalMirrorView({ kpis, onJoyceOpen }: DigitalMirrorViewProps) {
  const [location, setLocation] = useLocation();
  
  // Parse URL parameters
  const searchParams = new URLSearchParams(location.split('?')[1] || '');
  const urlMode = searchParams.get('mode') as "era" | "sei" | null;
  const urlViewMode = searchParams.get('view') as "cards" | "sankey" | null;

  const [mode, setMode] = useState<"era" | "sei">(urlMode || "sei");
  const [viewMode, setViewMode] = useState<"cards" | "sankey">(urlViewMode || "cards");

  // Update state when URL changes
  useEffect(() => {
    const searchParams = new URLSearchParams(location.split('?')[1] || '');
    const urlMode = searchParams.get('mode') as "era" | "sei" | null;
    const urlViewMode = searchParams.get('view') as "cards" | "sankey" | null;

    if (urlMode && urlMode !== mode) {
      setMode(urlMode);
    }
    if (urlViewMode && urlViewMode !== viewMode) {
      setViewMode(urlViewMode);
    }
  }, [location]);

  // Update URL when mode or viewMode changes
  useEffect(() => {
    const params = new URLSearchParams();
    if (mode !== "sei") params.set('mode', mode);
    if (viewMode !== "cards") params.set('view', viewMode);

    const newSearch = params.toString();
    const currentPath = location.split('?')[0];
    const newLocation = newSearch ? `${currentPath}?${newSearch}` : currentPath;

    if (newLocation !== location) {
      setLocation(newLocation);
    }
  }, [mode, viewMode, setLocation]);
  const [selectedKPIs, setSelectedKPIs] = useState<string[]>([]);
  const [expandedKPI, setExpandedKPI] = useState<MirrorKPIData | null>(null);
  const [expandedLayers, setExpandedLayers] = useState({
    enterprise: true,
    product: true,
    customer: true
  });

  const [filters, setFilters] = useState<FilterState>({
    layer: "all",
    category: "all",
    status: "all",
    persona: "executive",
    fogScore: "all",
    search: "",
    sortBy: "gap"
  });

  const filteredKPIs = useMemo(() => {
    let filtered = kpis.filter(kpi => {
      if (filters.layer !== "all" && kpi.layer !== filters.layer) return false;
      if (filters.category !== "all" && kpi.category !== filters.category) return false;
      if (filters.status !== "all" && kpi.status !== filters.status) return false;
      if (filters.fogScore === "high" && kpi.fogScore < 70) return false;
      if (filters.fogScore === "low" && kpi.fogScore >= 70) return false;
      if (filters.search && !kpi.name.toLowerCase().includes(filters.search.toLowerCase())) return false;
      return true;
    });

    // Sort
    switch (filters.sortBy) {
      case "gap":
        filtered.sort((a, b) => {
          const aGap = a.target ? Math.abs(a.currentValue - a.target) : 0;
          const bGap = b.target ? Math.abs(b.currentValue - b.target) : 0;
          return bGap - aGap;
        });
        break;
      case "upside":
        filtered.sort((a, b) => (b.executionAlphaImpact || 0) - (a.executionAlphaImpact || 0));
        break;
      case "benchmark":
        filtered.sort((a, b) => Math.abs(b.benchmarkDelta) - Math.abs(a.benchmarkDelta));
        break;
      case "trend":
        filtered.sort((a, b) => {
          const aTrend = a.trend[a.trend.length - 1] - a.trend[a.trend.length - 2];
          const bTrend = b.trend[b.trend.length - 1] - b.trend[b.trend.length - 2];
          return aTrend - bTrend; // Worst trend first
        });
        break;
    }

    return filtered;
  }, [kpis, filters]);

  const activeFilterCount = useMemo(() => {
    let count = 0;
    if (filters.layer !== "all") count++;
    if (filters.category !== "all") count++;
    if (filters.status !== "all") count++;
    if (filters.fogScore !== "all") count++;
    if (filters.search) count++;
    return count;
  }, [filters]);

  const kpisByLayer = useMemo(() => {
    return {
      enterprise: filteredKPIs.filter(kpi => kpi.layer === "enterprise"),
      product: filteredKPIs.filter(kpi => kpi.layer === "product"),
      customer: filteredKPIs.filter(kpi => kpi.layer === "customer")
    };
  }, [filteredKPIs]);

  const handleKPIClick = (kpi: MirrorKPIData) => {
    setExpandedKPI(kpi);
  };

  const handleJoyceAsk = (kpi: MirrorKPIData) => {
    const question = `Analyze the ${kpi.name} KPI performance. Current value is ${kpi.currentValue}${kpi.unit}, target is ${kpi.target}${kpi.unit}. What are the key drivers and recommended actions?`;
    onJoyceOpen?.([question]);
  };

  const handleBulkJoyceAsk = () => {
    const selectedKPIData = kpis.filter(kpi => selectedKPIs.includes(kpi.id));
    const questions = selectedKPIData.map(kpi => 
      `${kpi.name}: ${kpi.currentValue}${kpi.unit} (target: ${kpi.target}${kpi.unit})`
    );
    const bulkQuestion = `Analyze these KPIs together and identify systemic issues: ${questions.join(", ")}`;
    onJoyceOpen?.([bulkQuestion]);
  };

  const toggleLayerExpansion = (layer: keyof typeof expandedLayers) => {
    setExpandedLayers(prev => ({ ...prev, [layer]: !prev[layer] }));
  };




  const LayerSection = ({ 
    title, 
    layer, 
    kpis: layerKPIs 
  }: { 
    title: string; 
    layer: keyof typeof expandedLayers; 
    kpis: MirrorKPIData[] 
  }) => (
    <div className="space-y-4">
      <Collapsible 
        open={expandedLayers[layer]} 
        onOpenChange={() => toggleLayerExpansion(layer)}
      >
        <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
          <h3 className="font-semibold text-gray-900">{title}</h3>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {layerKPIs.length} KPIs
            </Badge>
            <ChevronDown className={`w-4 h-4 transition-transform ${expandedLayers[layer] ? 'rotate-180' : ''}`} />
          </div>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 pt-2">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {layerKPIs.map(kpi => (
              <MirrorKPICard
                key={kpi.id}
                kpi={kpi}
                mode={mode}
                onClick={handleKPIClick}
                onJoyceAsk={handleJoyceAsk}
                isSelected={selectedKPIs.includes(kpi.id)}
              />
            ))}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );

  return (
      <div className="flex h-full">
        {/* Main KPI Grid */}
        <div className={`flex-1 overflow-auto transition-all duration-300 ${expandedKPI ? 'mr-96' : ''}`}>
          {/* Header */}
          <div className="bg-white border-b border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Digital Mirror</h1>
                <p className="text-gray-600 mt-1">
                  Enterprise-wide KPI monitoring with causal linkages
                </p>
              </div>
              
              <div className="flex items-center gap-4">
                {/* ERA vs SEI Toggle */}
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Mode:</span>
                  <ToggleGroup 
                    type="single" 
                    value={mode} 
                    onValueChange={(value) => value && setMode(value as "era" | "sei")}
                    className="h-9"
                  >
                    <ToggleGroupItem value="era" className="text-sm px-4">
                      ERA
                    </ToggleGroupItem>
                    <ToggleGroupItem value="sei" className="text-sm px-4">
                      SEI
                    </ToggleGroupItem>
                  </ToggleGroup>
                </div>

                {/* Bulk Actions */}
                {selectedKPIs.length > 0 && (
                  <div className="flex items-center gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={handleBulkJoyceAsk}
                    >
                      Ask Joyce ({selectedKPIs.length})
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="w-4 h-4 mr-1" />
                      Export
                    </Button>
                  </div>
                )}

                {/* View Toggle */}
                <ToggleGroup
                  type="single"
                  value={viewMode}
                  onValueChange={(value) => value && setViewMode(value as "cards" | "sankey")}
                  className="h-9 mr-16"
                >
                  <ToggleGroupItem value="cards">
                    <Grid className="w-4 h-4" />
                  </ToggleGroupItem>
                  <ToggleGroupItem value="sankey">
                    <GitBranch className="w-4 h-4" />
                  </ToggleGroupItem>
                </ToggleGroup>
              </div>
            </div>
          </div>

          {viewMode === "cards" && (
            <>
              {/* Filter Toolbar */}
              <FilterToolbar 
                filters={filters}
                onFiltersChange={setFilters}
                activeFilterCount={activeFilterCount}
              />

              {/* KPI Layers with gradient connections background */}
              <div className="relative">
                {/* Background SVG for gradient connections */}
                <svg className="absolute inset-0 w-full h-full pointer-events-none" style={{ zIndex: 1 }}>
                  <defs>
                    <linearGradient id="cardFlowHigh" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.3" />
                      <stop offset="100%" stopColor="#1D4ED8" stopOpacity="0.1" />
                    </linearGradient>
                    <linearGradient id="cardFlowMedium" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" stopColor="#F59E0B" stopOpacity="0.3" />
                      <stop offset="100%" stopColor="#D97706" stopOpacity="0.1" />
                    </linearGradient>
                    <linearGradient id="cardFlowLow" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" stopColor="#6B7280" stopOpacity="0.2" />
                      <stop offset="100%" stopColor="#4B5563" stopOpacity="0.05" />
                    </linearGradient>
                  </defs>
                  
                  {/* Flowing connections between layers */}

                </svg>

                <div className="relative z-10 p-6 space-y-6">
                  <LayerSection 
                    title="Enterprise Layer" 
                    layer="enterprise" 
                    kpis={kpisByLayer.enterprise} 
                  />
                  <LayerSection 
                    title="Product/Service Layer" 
                    layer="product" 
                    kpis={kpisByLayer.product} 
                  />
                  <LayerSection 
                    title="Customer Journey Layer" 
                    layer="customer" 
                    kpis={kpisByLayer.customer} 
                  />
                </div>
              </div>
            </>
          )}


          
          {viewMode === "sankey" && (
            <SankeyKPIDiagram
              kpis={filteredKPIs}
              onKPIClick={handleKPIClick}
              mode={mode}
            />
          )}
        </div>

        {/* Right Panel - KPI Details */}
        {expandedKPI && (
          <div className="fixed right-0 top-[73px] w-96 h-[calc(100vh-73px)] bg-white border-l border-gray-200 shadow-lg z-10 overflow-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900">KPI Details</h3>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => setExpandedKPI(null)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">{expandedKPI.name}</h4>
                  <div className="text-3xl font-bold text-gray-900">
                    {expandedKPI.currentValue}{expandedKPI.unit}
                  </div>
                </div>

                {/* Historical Chart Placeholder */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">12-Month Trend</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {expandedKPI.trendData && expandedKPI.trendData.length > 0 ? (
                      <div className="h-40">
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart data={expandedKPI.trendData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="period" tick={{ fontSize: 10 }} />
                            <YAxis
                              width={40}
                              tick={{ fontSize: 10 }}
                              tickCount={5}
                              allowDecimals
                              domain={["dataMin - 0.5", "dataMax + 0.5"]}
                              tickFormatter={(v) =>
                                expandedKPI.unit === "%" ? `${v}%` : `${v}`
                              }
                            />
                            <RechartsTooltip
                              formatter={(value: any) =>
                                expandedKPI.unit === "%" ? `${value}%` : value
                              }
                            />
                            <Line
                              type="monotone"
                              dataKey="value"
                              stroke="#3B82F6"
                              strokeWidth={2}
                              dot={false}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    ) : (
                      <div className="h-32 bg-gray-50 rounded flex items-center justify-center text-gray-500 text-sm">
                        Historical chart visualization
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Peer Comparisons */}
                {expandedKPI.benchmark && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Peer Comparison</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Current</span>
                          <span className="font-medium">{expandedKPI.currentValue}{expandedKPI.unit}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Peer Average</span>
                          <span className="font-medium">{expandedKPI.benchmark}{expandedKPI.unit}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Delta</span>
                          <span className={`font-medium ${expandedKPI.benchmarkDelta > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {expandedKPI.benchmarkDelta > 0 ? '+' : ''}{expandedKPI.benchmarkDelta.toFixed(1)}%
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Causal Drivers */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Key Drivers</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {expandedKPI.drivers.map((driver, index) => (
                        <div key={index} className="text-sm text-gray-700">
                          • {driver}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Recommended Actions */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Joyce Recommendations</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {expandedKPI.joyceInsight && (
                        <p className="text-sm text-gray-700">{expandedKPI.joyceInsight}</p>
                      )}
                      <Button 
                        className="w-full"
                        onClick={() => handleJoyceAsk(expandedKPI)}
                      >
                        Get Detailed Analysis
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        )}
      </div>
  );
}
