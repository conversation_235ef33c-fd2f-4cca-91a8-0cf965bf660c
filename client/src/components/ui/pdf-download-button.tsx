import { useState } from "react";
import { But<PERSON> } from "./button";
import { Download, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface PDFDownloadButtonProps {
  reportType: 'sei' | 'era';
  companySymbol: string;
  format?: 'A4' | 'Letter';
  landscape?: boolean;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
  className?: string;
  children?: React.ReactNode;
}

export function PDFDownloadButton({
  reportType,
  companySymbol,
  format = 'A4',
  landscape = false,
  variant = 'outline',
  size = 'default',
  className,
  children
}: PDFDownloadButtonProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();

  const handleDownload = async () => {
    setIsGenerating(true);
    
    try {
      const params = new URLSearchParams({
        format,
        landscape: landscape.toString()
      });
      
      const response = await fetch(
        `/api/reports/pdf/${reportType}/${companySymbol.toLowerCase()}?${params}`,
        {
          method: 'GET',
          headers: {
            'Accept': 'application/pdf',
          }
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to generate PDF: ${response.status}`);
      }

      // Get the PDF blob
      const pdfBlob = await response.blob();
      
      // Create download link
      const url = window.URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      
      // Get filename from response headers or generate one
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = `${reportType.toLowerCase()}-report-${companySymbol.toLowerCase()}-${new Date().toISOString().split('T')[0]}.pdf`;
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }
      
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      
      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast({
        title: "PDF Generated Successfully",
        description: `${reportType.toUpperCase()} report has been downloaded.`,
      });
      
    } catch (error) {
      console.error('PDF download error:', error);
      toast({
        title: "PDF Generation Failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Button
      onClick={handleDownload}
      disabled={isGenerating}
      variant={variant}
      size={size}
      className={className}
    >
      {isGenerating ? (
        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
      ) : (
        <Download className="w-4 h-4 mr-2" />
      )}
      {children || (isGenerating ? 'Generating PDF...' : 'Download PDF')}
    </Button>
  );
}