import { MirrorKPIData } from "@/components/digital-mirror/mirror-kpi-card";
import { DollarSign, TrendingUp, Users, Clock, Target, Zap, Heart, Shield, Building, Activity } from "lucide-react";

interface CVSEnterpriseData {
  coreKpiPerformance: {
    metrics: Array<{
      indicator: string;
      actual: string;
      target: string;
      peer: string;
      status: string;
      commentary: string;
    }>;
  };
  fogScore: {
    overall: {
      value: number;
      maxValue: number;
      status: string;
    };
  };
}

interface CVSProductsData {
  segmentData: {
    [key: string]: {
      performanceAnalysis: {
        keyMetrics: Array<{
          name: string;
          value: string;
          trend: string;
          description: string;
        }>;
      };
      journeyStages: {
        [stage: string]: {
          joyScore: number;
          inputsOutputs: {
            kpi: string;
          };
        };
      };
    };
  };
}

interface CVSCustomerData {
  segmentData: {
    [key: string]: {
      keyInsights: Array<{
        text: string;
        status: string;
      }>;
    };
  };
  sharedCharts: {
    churnRateTrends: {
      chartData: Array<{
        month: string;
        premium: number;
        standard: number;
        basic: number;
      }>;
    };
    customerLifetimeValue: {
      chartData: Array<{
        segment: string;
        verizon: number;
        benchmark: number;
      }>;
    };
  };
}

export function adaptCompanyDataToMirrorKPIs(
  enterpriseData: any,
  productsData: any, 
  customerData: any,
  companySymbol: string,
  eraExecDashboard?: any
): MirrorKPIData[] {
  const kpis: MirrorKPIData[] = [];

  // Helpers for monetary impact calculations
  const parseUSD = (s: any): number => {
    try {
      if (typeof s !== "string") return 0;
      const num = parseFloat(s.replace(/[^\d.-]/g, ""));
      if (isNaN(num)) return 0;
      const upper = s.toUpperCase();
      const scale = upper.includes("B") ? 1_000_000_000 : upper.includes("M") ? 1_000_000 : upper.includes("K") ? 1_000 : 1;
      return num * scale;
    } catch { return 0; }
  };

  const toUSDFromUnit = (val: number, unit: string): number => {
    switch (unit) {
      case "B": return val * 1_000_000_000;
      case "M": return val * 1_000_000;
      case "$": return val;
      default: return val;
    }
  };

  const computeImpactUSD = (current: number, target: number | undefined, unit: string, revenueUSD: number, ebitdaUSD: number): number => {
    if (target == null) return 0;
    if (unit === "%") return Math.max(0, target - current) / 100 * revenueUSD;
    if (unit === "x") return Math.max(0, current - target) * ebitdaUSD; // debt reduction needed to reach target leverage
    if (unit === "B" || unit === "M" || unit === "$") return Math.max(0, target - current) ? toUSDFromUnit(Math.max(0, target - current), unit) : 0;
    return 0;
  };

  const computeAdvantageVsPeerUSD = (current: number, peer: number | undefined, unit: string, revenueUSD: number, ebitdaUSD: number): number => {
    if (peer == null) return 0;
    if (unit === "%") return Math.max(0, current - peer) / 100 * revenueUSD;
    if (unit === "x") return Math.max(0, peer - current) * ebitdaUSD; // lower leverage than peers → debt advantage
    if (unit === "B" || unit === "M" || unit === "$") return Math.max(0, current - peer) ? toUSDFromUnit(Math.max(0, current - peer), unit) : 0;
    return 0;
  };

  // Try pulling TTM revenue for converting percent gaps into dollars
  const revenueUSD = parseUSD(enterpriseData?.executiveDashboard?.revenue?.value);

  // Estimate EBITDA dollars from EBITDA margin × revenue (if available) for leverage conversions (Net Debt/EBITDA)
  let ebitdaMarginPct = 0;
  try {
    const metrics = enterpriseData?.coreKpiPerformance?.metrics || [];
    const m = Array.isArray(metrics)
      ? metrics.find((mm: any) => String(mm?.indicator || "").toLowerCase().includes("ebitda margin"))
      : undefined;
    if (m?.actual) {
      const v = parseFloat(String(m.actual).replace(/[^\d.-]/g, ""));
      if (!Number.isNaN(v)) ebitdaMarginPct = v;
    }
  } catch {}
  const ebitdaUSD = revenueUSD && ebitdaMarginPct ? (revenueUSD * ebitdaMarginPct) / 100 : 0;

  // Helper: extract a quarterly series from ERA exec dashboard by dataset label
  const getEraSeries = (label: string): { period: string; value: number }[] | undefined => {
    try {
      if (!label || !eraExecDashboard?.chartConfigurations?.kpiTrends?.data) return undefined;
      const data = eraExecDashboard.chartConfigurations.kpiTrends.data;
      const labels: string[] = data.labels || [];
      const ds = (data.datasets || []).find(
        (d: any) => typeof d.label === "string" && d.label.toLowerCase() === label.toLowerCase()
      );
      if (!ds?.data || !labels.length) return undefined;
      return labels.map((l: string, i: number) => ({
        period: l.replace(/\s+/g, "-"),
        value: Number(ds.data[i]),
      }));
    } catch {
      return undefined;
    }
  };

  // Build a normalized map of ERA KPI trend series for generic matching
  const normalizeLabel = (s: string) =>
    (s || "").toLowerCase().replace(/[%]/g, "").replace(/[^a-z0-9 ]/g, "").replace(/\s+/g, " ").trim();

  const eraSeriesMap: Record<string, { period: string; value: number }[]> = (() => {
    const map: Record<string, { period: string; value: number }[]> = {};
    try {
      const data = eraExecDashboard?.chartConfigurations?.kpiTrends?.data;
      if (data?.datasets && data?.labels) {
        for (const ds of data.datasets) {
          if (typeof ds.label === "string" && Array.isArray(ds.data)) {
            const norm = normalizeLabel(ds.label);
            map[norm] = data.labels.map((l: string, i: number) => ({
              period: String(l).replace(/\s+/g, "-"),
              value: Number(ds.data[i]),
            }));
          }
        }
      }
    } catch {}
    return map;
  })();

  // Enterprise Layer KPIs
  if (enterpriseData.coreKpiPerformance?.metrics) {
    enterpriseData.coreKpiPerformance.metrics.forEach((metric: { indicator: string; actual: string; target: string; peer: string; status: string; commentary: string; }, index: number) => {
    const actualValue = parseFloat(metric.actual.replace(/[^\d.-]/g, ''));
    const targetValue = parseFloat(metric.target.replace(/[^\d.-]/g, ''));
    const peerValue = parseFloat(metric.peer.replace(/[^\d.-]/g, ''));
    
    let unit = "%";
    if (metric.actual.includes("$")) unit = "$";
    if (metric.actual.includes("x")) unit = "x";
    if (metric.actual.includes("B")) unit = "B";

    const status = metric.status === "positive" ? "on-track" : 
                  metric.status === "warning" ? "at-risk" : "off-track";

    // Default sparkline (fallback)
    let trend = [actualValue - 1, actualValue - 0.5, actualValue - 0.2, actualValue];
    let trendData: { period: string; value: number }[] | undefined = undefined;

    // Prefer existing ERA Exec Dashboard quarterly datasets when available
    const normIndicator = normalizeLabel(metric.indicator);
    const series = eraSeriesMap[normIndicator] || getEraSeries(metric.indicator);
    if (series && series.length) {
      trendData = series;
      trend = series.map((m) => m.value);
    }

    kpis.push({
      id: `enterprise-${metric.indicator.toLowerCase().replace(/\s+/g, '-')}`,
      name: metric.indicator,
      icon: metric.indicator.includes("Revenue") ? TrendingUp :
            metric.indicator.includes("EPS") ? DollarSign :
            metric.indicator.includes("Cash") ? DollarSign : Building,
      currentValue: actualValue,
      unit: unit,
      target: targetValue,
      benchmark: peerValue,
      trend,
      trendData,
      variance: status === "on-track" ? "up" : status === "at-risk" ? "flat" : "down",
      benchmarkDelta: ((actualValue - peerValue) / peerValue) * 100,
      fogScore: enterpriseData.fogScore.overall.value * 10, // Convert to 0-100 scale
      maturityLevel: "Advanced",
      impactUSD: computeImpactUSD(actualValue, targetValue, unit, revenueUSD, ebitdaUSD),
      advantageVsPeerUSD: computeAdvantageVsPeerUSD(actualValue, peerValue, unit, revenueUSD, ebitdaUSD),
      executionAlphaImpact: computeImpactUSD(actualValue, targetValue, unit, revenueUSD, ebitdaUSD),
      drivers: [metric.commentary.split('.')[0]],
      joyceInsight: `Focus on ${metric.indicator.toLowerCase()} improvement through operational efficiency.`,
      layer: "enterprise",
      category: "financial",
      status: status as "on-track" | "at-risk" | "off-track"
    });
    });
  }

  // Product Layer KPIs
  if (productsData.segmentData) {
    Object.entries(productsData.segmentData).forEach(([segmentKey, segment]: [string, any]) => {
      if (segment.performanceAnalysis?.keyMetrics) {
        (segment.performanceAnalysis.keyMetrics as Array<{ name: string; value: string; trend: string; description: string; }>).forEach((metric: { name: string; value: string; trend: string; description: string; }, index: number) => {
      const value = parseFloat(metric.value.replace(/[^\d.-]/g, ''));
      const unit = metric.value.includes("%") ? "%" : 
                   metric.value.includes("B") ? "B" :
                   metric.value.includes("M") ? "M" : "";

      const status = metric.trend === "positive" ? "on-track" : 
                    metric.trend === "stable" ? "at-risk" : "off-track";

      let pTrend = [value * 0.95, value * 0.97, value * 0.99, value];
      let pTrendData: { period: string; value: number }[] | undefined = undefined;

      // Monetary impact calculations for product KPIs (best effort using enterprise revenue for % KPIs)
      const impactUSD = computeImpactUSD(value, value * 1.1, unit, revenueUSD, ebitdaUSD);
      const advantageUSD = computeAdvantageVsPeerUSD(value, value * 1.05, unit, revenueUSD, ebitdaUSD);

      // If this is a Churn Rate metric, attach monthly churn trend (average across segments)
      if (metric.name.toLowerCase().includes("churn") && customerData?.sharedCharts?.churnRateTrends?.chartData) {
        try {
          const rows = customerData.sharedCharts.churnRateTrends
            .chartData as Array<{ month: string; premium: number; standard: number; basic: number }>;
          pTrendData = rows.map(r => ({
            period: r.month,
            value: Number(((r.premium + r.standard + r.basic) / 3).toFixed(2)),
          }));
          if (pTrendData.length) {
            pTrend = pTrendData.map(d => d.value);
          }
        } catch {}
      }

      kpis.push({
        id: `product-${segmentKey}-${metric.name.toLowerCase().replace(/\s+/g, '-')}`,
        name: `${metric.name} (${segmentKey.replace(/-/g, ' ')})`,
        icon: segmentKey.includes("pharmacy") ? Shield :
              segmentKey.includes("health") ? Heart : Building,
        currentValue: value,
        unit: unit,
        target: value * 1.1, // Assume 10% improvement target
        benchmark: value * 1.05, // Assume 5% above current as benchmark
        trend: pTrend,
        trendData: pTrendData,
        variance: metric.trend === "positive" ? "up" : "flat",
        benchmarkDelta: -5, // Placeholder
        fogScore: 85,
        maturityLevel: "Developing",
        impactUSD: impactUSD,
        advantageVsPeerUSD: advantageUSD,
        executionAlphaImpact: impactUSD,
        drivers: [metric.description],
        joyceInsight: `Optimize ${metric.name.toLowerCase()} through targeted improvements.`,
        layer: "product",
        category: metric.name.includes("Revenue") ? "financial" : "operational",
        status: status as "on-track" | "at-risk" | "off-track"
      });
        });
      }
    });
  }

  // Customer Journey Layer KPIs
  // Extract journey stage data from products data
  if (productsData.segmentData) {
    Object.entries(productsData.segmentData).forEach(([segmentKey, segment]: [string, any]) => {
      if ((segment as any).journeyStages) {
        Object.entries((segment as any).journeyStages as Record<string, any>).forEach(([stage, stageData]: [string, any]) => {
      if (stageData.joyScore && stageData.inputsOutputs) {
        const joyScore = stageData.joyScore;
        const kpiText = stageData.inputsOutputs.kpi;
        
        // Extract value from KPI text (e.g., "CPA: $11.80" -> 11.80)
        const valueMatch = kpiText.match(/[\d.]+/);
        const value = valueMatch ? parseFloat(valueMatch[0]) : joyScore;
        
        const unit = kpiText.includes("$") ? "$" :
                     kpiText.includes("%") ? "%" :
                     kpiText.includes("min") ? " min" :
                     kpiText.includes("days") ? " days" : "";

        // Monetary impact calculations for customer journey KPIs (only when unit is % or $/M/B)
        const cImpactUSD = computeImpactUSD(value, value * 1.15, unit.trim(), revenueUSD, ebitdaUSD);
        const cAdvUSD = computeAdvantageVsPeerUSD(value, value * 1.08, unit.trim(), revenueUSD, ebitdaUSD);

        kpis.push({
          id: `customer-${segmentKey}-${stage}`,
          name: `${stage.charAt(0).toUpperCase() + stage.slice(1)} (${segmentKey.replace(/-/g, ' ')})`,
          icon: stage === "awareness" ? Users :
                stage === "consideration" ? Target :
                stage === "purchase" ? DollarSign :
                stage === "services" ? Activity :
                stage === "loyalty" ? Heart : Clock,
          currentValue: value,
          unit: unit,
          target: value * 1.15, // Assume 15% improvement target for journey metrics
          benchmark: value * 1.08, // Assume 8% above current as benchmark
          trend: [value * 0.92, value * 0.95, value * 0.98, value],
          variance: joyScore > 85 ? "up" : joyScore > 75 ? "flat" : "down",
          benchmarkDelta: -8, // Placeholder
          fogScore: 75,
          maturityLevel: "Developing",
          impactUSD: cImpactUSD,
          advantageVsPeerUSD: cAdvUSD,
          executionAlphaImpact: cImpactUSD,
          drivers: [kpiText],
          joyceInsight: `Improve ${stage} stage performance through process optimization.`,
          layer: "customer",
          category: "customer",
          status: joyScore > 85 ? "on-track" : joyScore > 75 ? "at-risk" : "off-track"
        });
        }
        });
      }
    });
  }

  // Add company-specific high-impact KPIs based on company symbol
  const companySpecificKPIs = getCompanySpecificKPIs(companySymbol);
  kpis.push(...companySpecificKPIs);

  return kpis;
}

function getCompanySpecificKPIs(companySymbol: string): MirrorKPIData[] {
  const baseKPIs: MirrorKPIData[] = [];

  switch (companySymbol.toUpperCase()) {
    case 'CVS':
      baseKPIs.push({
        id: "customer-onboarding-lead-time",
        name: "Digital Onboarding Lead Time",
        icon: Clock,
        currentValue: 14,
        unit: " days",
        target: 5,
        benchmark: 6,
        trend: [16, 15.5, 15, 14.8, 14.2, 14],
        variance: "down",
        benchmarkDelta: 133,
        fogScore: 75,
        impactUSD: *********,
        executionAlphaImpact: *********,
        drivers: [
          "Manual identity verification process",
          "Inconsistent staff training across regions",
          "Legacy system integration bottlenecks"
        ],
        joyceInsight: "Automate ID verification and standardize training to reduce onboarding time to 5 days.",
        layer: "customer",
        category: "operational", 
        status: "off-track"
      });
      break;

    case 'VZ':
      baseKPIs.push({
        id: "customer-5g-adoption-rate",
        name: "5G Service Adoption Rate",
        icon: Zap,
        currentValue: 45,
        unit: "%",
        target: 65,
        benchmark: 52,
        trend: [38, 40, 42, 43, 44, 45],
        variance: "up",
        benchmarkDelta: -13.5,
        fogScore: 88,
        impactUSD: 890000000,
        executionAlphaImpact: 890000000,
        drivers: [
          "Limited 5G device penetration",
          "Customer education gaps on 5G benefits",
          "Network coverage optimization needed"
        ],
        joyceInsight: "Accelerate 5G device promotions and expand network coverage to drive adoption.",
        layer: "customer",
        category: "operational",
        status: "at-risk"
      });
      break;

    case 'MA':
      baseKPIs.push({
        id: "customer-transaction-processing-time",
        name: "Transaction Processing Speed",
        icon: Zap,
        currentValue: 2.1,
        unit: " seconds",
        target: 1.5,
        benchmark: 1.8,
        trend: [2.4, 2.3, 2.2, 2.15, 2.12, 2.1],
        variance: "down",
        benchmarkDelta: 16.7,
        fogScore: 92,
        impactUSD: *********,
        executionAlphaImpact: *********,
        drivers: [
          "Legacy payment infrastructure",
          "Cross-border processing complexity",
          "Peak load optimization needs"
        ],
        joyceInsight: "Modernize payment infrastructure and optimize peak load handling to reduce processing time.",
        layer: "customer", 
        category: "operational",
        status: "at-risk"
      });
      break;

    case 'ACN':
      baseKPIs.push({
        id: "customer-project-delivery-time",
        name: "Project Delivery Cycle Time",
        icon: Clock,
        currentValue: 180,
        unit: " days",
        target: 120,
        benchmark: 140,
        trend: [195, 190, 185, 183, 181, 180],
        variance: "down",
        benchmarkDelta: 28.6,
        fogScore: 80,
        impactUSD: *********,
        executionAlphaImpact: *********,
        drivers: [
          "Client requirement changes mid-project",
          "Resource allocation optimization",
          "Agile methodology adoption gaps"
        ],
        joyceInsight: "Implement agile practices and improve resource allocation to reduce delivery cycles.",
        layer: "customer",
        category: "operational",
        status: "off-track"
      });
      break;

    default:
      // Generic KPI for unknown companies
      baseKPIs.push({
        id: "customer-satisfaction-score",
        name: "Customer Satisfaction Score",
        icon: Heart,
        currentValue: 75,
        unit: "/100",
        target: 85,
        benchmark: 80,
        trend: [72, 73, 74, 74.5, 74.8, 75],
        variance: "up",
        benchmarkDelta: -6.25,
        fogScore: 70,
        impactUSD: 50000000,
        executionAlphaImpact: 50000000,
        drivers: ["Customer experience optimization needed"],
        joyceInsight: "Focus on customer experience improvements to boost satisfaction scores.",
        layer: "customer",
        category: "customer",
        status: "at-risk"
      });
  }

  return baseKPIs;
}
