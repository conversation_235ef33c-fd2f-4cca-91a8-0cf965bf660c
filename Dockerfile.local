# Local development Dockerfile - mirrors production but includes dev dependencies for DB migrations
# This ensures local environment matches production behavior

FROM node:20-bookworm-slim AS builder
WORKDIR /app

# Install ALL dependencies (including dev deps needed for drizzle-kit)
COPY package.json package-lock.json ./
ENV PUPPETEER_SKIP_DOWNLOAD=true PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
RUN npm ci

# Copy source code
COPY . .

# Build the application (same as production)
RUN npm run build

# Production runtime with DB migration capabilities
FROM node:20-bookworm-slim AS runner
WORKDIR /app

# Install ALL dependencies (we need drizzle-kit for migrations)
COPY package.json package-lock.json ./
ENV PUPPETEER_SKIP_DOWNLOAD=true PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
RUN npm ci

# Copy built artifacts (same as production)
COPY --from=builder /app/dist ./dist

# Copy client/public data (same as production)
COPY --from=builder /app/client/public ./client/public

# Copy source files needed for migrations
COPY --from=builder /app/drizzle.config.ts ./drizzle.config.ts
COPY --from=builder /app/shared ./shared
# Copy server/files for /files/* route
COPY --from=builder /app/server/files ./server/files

# Install postgresql-client for health checks
RUN apt-get update && apt-get install -y postgresql-client && rm -rf /var/lib/apt/lists/*

# Environment (same as production)
ENV NODE_ENV=production
ENV PORT=5000

EXPOSE 5000

# Create startup script that handles DB migrations then starts the app
RUN echo '#!/bin/bash\nset -e\n\n# Wait for database to be ready\necho "Waiting for database to be ready..."\nwhile ! pg_isready -h db -p 5432 -U rejoyce -d rejoyce_dev -q; do\n  echo "Database is not ready, waiting..."\n  sleep 2\ndone\necho "Database is ready!"\n\n# Push database schema (same as what Railway would do)\necho "Pushing database schema..."\nnpm run db:push\n\n# Start the application (same as production)\necho "Starting application..."\nnode dist/index.js' > /app/start-local.sh && chmod +x /app/start-local.sh

CMD ["/app/start-local.sh"]
