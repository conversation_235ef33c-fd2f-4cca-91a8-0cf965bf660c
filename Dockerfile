# syntax=docker/dockerfile:1

# Build stage: install all deps and build client + server
FROM node:20-bookworm-slim AS builder
WORKDIR /app

# Install dependencies first (leverage layer caching)
COPY package.json package-lock.json ./
ENV PUPPETEER_SKIP_DOWNLOAD=true PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
RUN npm ci

# Copy the rest of the source
COPY . .

# Build client (Vite) into dist/public and bundle server into dist/index.js
RUN npm run build

# Runtime stage: only what's needed to run the app
FROM node:20-bookworm-slim AS runner
WORKDIR /app

# [disabled] Install system dependencies for Puppeteer (not needed while PDF generation is disabled)
# RUN apt-get update && apt-get install -y \
#     chromium \
#     libglib2.0-0 \
#     libnss3 \
#     libgconf-2-4 \
#     libxss1 \
#     libxtst6 \
#     libxrandr2 \
#     libasound2 \
#     libpangocairo-1.0-0 \
#     libatk1.0-0 \
#     libcairo-gobject2 \
#     libgtk-3-0 \
#     libgdk-pixbuf2.0-0 \
#     libxcomposite1 \
#     libxcursor1 \
#     libxdamage1 \
#     libxi6 \
#     libxrender1 \
#     libxss1 \
#     libxtst6 \
#     ca-certificates \
#     fonts-liberation \
#     libappindicator1 \
#     libnss3 \
#     lsb-release \
#     xdg-utils \
#     wget \
#     --no-install-recommends \
#     && apt-get clean \
#     && rm -rf /var/lib/apt/lists/*

# Install only production dependencies
COPY package.json package-lock.json ./
ENV PUPPETEER_SKIP_DOWNLOAD=true PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
RUN npm ci --omit=dev

# Copy built artifacts
COPY --from=builder /app/dist ./dist

# Copy client/public data for routes that read from the source tree
# (server/routes.ts reads ../client/public/data/companies at runtime)
COPY --from=builder /app/client/public ./client/public

# Copy server/files so /files/* route can serve assets in container
COPY --from=builder /app/server/files ./server/files

# Environment
ENV NODE_ENV=production
ENV PORT=5000

# Railway will set PORT automatically; this EXPOSE is informational
EXPOSE 5000

# Start the server (serves API and static files from dist/public)
CMD ["node", "dist/index.js"]
